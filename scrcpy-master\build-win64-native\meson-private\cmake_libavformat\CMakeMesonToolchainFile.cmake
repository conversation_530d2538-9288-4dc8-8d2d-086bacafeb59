######################################
###  AUTOMATICALLY GENERATED FILE  ###
######################################

# This file was generated from the configuration in the
# relevant meson machine file. See the meson documentation
# https://mesonbuild.com/Machine-files.html for more information

if(DEFINED MESON_PRELOAD_FILE)
    include("${MESON_PRELOAD_FILE}")
endif()

# CMake compiler state variables
# -- Variables for language c
set(CMAKE_C_COMPILER "C:/msys64/mingw64/bin/cc.EXE")
set(CMAKE_C_COMPILER_ID "GNU")
set(CMAKE_C_COMPILER_VERSION "15.1.0")
set(CMAKE_C_STANDARD_COMPUTED_DEFAULT "23")
set(CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT "ON")
set(CMAKE_C_PLATFORM_ID "MinGW")
set(CMAKE_C_COMPILER_FRONTEND_VARIANT "GNU")
set(CMAKE_AR "C:/msys64/mingw64/bin/ar.exe")
set(CMAKE_C_COMPILER_AR "C:/msys64/mingw64/bin/gcc-ar.exe")
set(CMAKE_RANLIB "C:/msys64/mingw64/bin/ranlib.exe")
set(CMAKE_C_COMPILER_RANLIB "C:/msys64/mingw64/bin/gcc-ranlib.exe")
set(CMAKE_LINKER "C:/msys64/mingw64/bin/ld.exe")
set(CMAKE_TAPI "CMAKE_TAPI-NOTFOUND")
set(CMAKE_COMPILER_IS_GNUCC "1")
set(CMAKE_C_COMPILER_LOADED "1")
set(CMAKE_C_COMPILER_ENV_VAR "CC")
set(CMAKE_C_COMPILER_ID_RUN "1")
set(CMAKE_C_SOURCE_FILE_EXTENSIONS "c" "m")
set(CMAKE_C_IGNORE_EXTENSIONS "h" "H" "o" "O" "obj" "OBJ" "def" "DEF" "rc" "RC")
set(CMAKE_C_LINKER_PREFERENCE "10")
set(CMAKE_C_STANDARD_LATEST "23")
set(CMAKE_C_COMPILE_FEATURES "c_std_90" "c_function_prototypes" "c_std_99" "c_restrict" "c_variadic_macros" "c_std_11" "c_static_assert" "c_std_17" "c_std_23")
set(CMAKE_C90_COMPILE_FEATURES "c_std_90" "c_function_prototypes")
set(CMAKE_C99_COMPILE_FEATURES "c_std_99" "c_restrict" "c_variadic_macros")
set(CMAKE_C11_COMPILE_FEATURES "c_std_11" "c_static_assert")
set(CMAKE_C17_COMPILE_FEATURES "c_std_17")
set(CMAKE_C23_COMPILE_FEATURES "c_std_23")
set(CMAKE_C_COMPILER_LINKER "C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe")
set(CMAKE_C_COMPILER_LINKER_ID "GNU")
set(CMAKE_C_COMPILER_LINKER_VERSION "2.44")
set(CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT "GNU")
set(CMAKE_C_COMPILER_WORKS "TRUE")
set(CMAKE_C_ABI_COMPILED "TRUE")
set(CMAKE_C_SIZEOF_DATA_PTR "8")
set(CMAKE_C_BYTE_ORDER "LITTLE_ENDIAN")
set(CMAKE_SIZEOF_VOID_P "8")
set(CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES "C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include" "C:/msys64/mingw64/include" "C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed")
set(CMAKE_C_IMPLICIT_LINK_LIBRARIES "mingw32" "gcc" "mingwex" "kernel32" "pthread" "advapi32" "shell32" "user32" "kernel32" "mingw32" "gcc" "mingwex" "kernel32")
set(CMAKE_C_IMPLICIT_LINK_DIRECTORIES "C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0" "C:/msys64/mingw64/lib/gcc" "C:/msys64/mingw64/x86_64-w64-mingw32/lib" "C:/msys64/mingw64/lib")
set(CMAKE_C_COMPILER_FORCED "1")


# Variables from meson
set(CMAKE_SIZEOF_VOID_P "8")
set(CMAKE_C_COMPILER "C:/msys64/mingw64/bin/cc.EXE")

