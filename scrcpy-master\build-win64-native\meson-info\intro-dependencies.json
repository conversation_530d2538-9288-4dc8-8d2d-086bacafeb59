[{"name": "libavformat", "type": "pkgconfig", "version": "61.7.100", "compile_args": [], "link_args": ["C:/msys64/mingw64/lib/libavformat.dll.a"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "libavcodec", "type": "pkgconfig", "version": "61.19.101", "compile_args": [], "link_args": ["C:/msys64/mingw64/lib/libavcodec.dll.a"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "liba<PERSON><PERSON>", "type": "pkgconfig", "version": "59.39.100", "compile_args": [], "link_args": ["C:/msys64/mingw64/lib/libavutil.dll.a"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "libswresample", "type": "pkgconfig", "version": "5.3.100", "compile_args": [], "link_args": ["C:/msys64/mingw64/lib/libswresample.dll.a"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "sdl2", "type": "pkgconfig", "version": "2.32.8", "compile_args": ["-IC:/msys64/mingw64/include/SDL2", "-Dmain=SDL_main"], "link_args": ["C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "libusb-1.0", "type": "pkgconfig", "version": "1.0.28", "compile_args": ["-IC:/msys64/mingw64/include/libusb-1.0"], "link_args": ["C:/msys64/mingw64/lib/libusb-1.0.dll.a"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}]