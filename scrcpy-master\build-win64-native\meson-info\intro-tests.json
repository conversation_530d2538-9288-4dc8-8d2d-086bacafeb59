[{"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_adb_parser.exe"], "env": {}, "name": "test_adb_parser", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_adb_parser@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_binary.exe"], "env": {}, "name": "test_binary", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_binary@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_audiobuf.exe"], "env": {}, "name": "test_audiobuf", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_audiobuf@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_cli.exe"], "env": {}, "name": "test_cli", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_cli@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_control_msg_serialize.exe"], "env": {}, "name": "test_control_msg_serialize", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_control_msg_serialize@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_device_msg_deserialize.exe"], "env": {}, "name": "test_device_msg_deserialize", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_device_msg_deserialize@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_orientation.exe"], "env": {}, "name": "test_orientation", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_orientation@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_strbuf.exe"], "env": {}, "name": "test_strbuf", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_strbuf@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_str.exe"], "env": {}, "name": "test_str", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_str@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_vecdeque.exe"], "env": {}, "name": "test_vecdeque", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_vecdeque@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}, {"cmd": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_vector.exe"], "env": {}, "name": "test_vector", "workdir": null, "timeout": 30, "suite": ["scrcpy"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["a172ced@@test_vector@exe"], "extra_paths": ["C:/msys64/mingw64/bin"]}]