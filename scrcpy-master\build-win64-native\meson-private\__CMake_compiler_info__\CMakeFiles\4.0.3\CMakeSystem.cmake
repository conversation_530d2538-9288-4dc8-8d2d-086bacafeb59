set(CMAKE_HOST_SYSTEM "Windows-10.0.26100")
set(CMAKE_HOST_SYSTEM_NAME "Windows")
set(CMAKE_HOST_SYSTEM_VERSION "10.0.26100")
set(CMAKE_HOST_SYSTEM_PROCESSOR "AMD64")

include("C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/__CMake_compiler_info__/CMakeMesonTempToolchainFile.cmake")

set(CMAKE_SYSTEM "Windows-10.0.26100")
set(CMAKE_SYSTEM_NAME "Windows")
set(CMAKE_SYSTEM_VERSION "10.0.26100")
set(CMAKE_SYSTEM_PROCESSOR "AMD64")

set(CMAKE_CROSSCOMPILING "FALSE")

set(CMAKE_SYSTEM_LOADED 1)
