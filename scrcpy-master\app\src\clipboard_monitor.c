#include "clipboard_monitor.h"

#include <assert.h>
#include <string.h>
#include <stdlib.h>

#include "controller.h"
#include "control_msg.h"
#include "events.h"
#include "util/log.h"

#ifdef _WIN32
#include <windows.h>
#include <SDL2/SDL.h>

// Windows-specific implementation
static const char *CLIPBOARD_WINDOW_CLASS = "ScrcpyClipboardMonitor";
static struct sc_clipboard_monitor *g_monitor = NULL;

// Forward declarations
static LRESULT CALLBACK clipboard_window_proc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
static DWORD WINAPI clipboard_monitor_thread(LPVOID param);
static void send_clipboard_to_device(struct sc_clipboard_monitor *monitor);

static LRESULT CALLBACK
clipboard_window_proc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    switch (msg) {
        case WM_CLIPBOARDUPDATE:
            LOGD("Clipboard updated - sending to device");
            if (g_monitor && g_monitor->monitoring) {
                send_clipboard_to_device(g_monitor);
            }
            return 0;
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

static void
send_clipboard_to_device(struct sc_clipboard_monitor *monitor) {
    assert(monitor);
    assert(monitor->controller);
    
    // Lấy clipboard text từ Windows
    char *text = SDL_GetClipboardText();
    if (!text) {
        LOGW("Could not get clipboard text: %s", SDL_GetError());
        return;
    }
    
    if (!*text) {
        // Empty text
        SDL_free(text);
        return;
    }
    
    // Tạo copy của text để gửi tới controller
    char *text_dup = strdup(text);
    SDL_free(text);
    if (!text_dup) {
        LOGW("Could not duplicate clipboard text");
        return;
    }
    
    // Tạo control message để set clipboard trên device
    struct sc_control_msg msg;
    msg.type = SC_CONTROL_MSG_TYPE_SET_CLIPBOARD;
    msg.set_clipboard.sequence = SC_SEQUENCE_INVALID; // Không cần ack
    msg.set_clipboard.text = text_dup;
    msg.set_clipboard.paste = false; // Chỉ set clipboard, không paste
    
    if (!sc_controller_push_msg(monitor->controller, &msg)) {
        LOGW("Could not send clipboard to device");
        free(text_dup);
    } else {
        LOGI("Clipboard sent to device");
    }
}

static DWORD WINAPI
clipboard_monitor_thread(LPVOID param) {
    struct sc_clipboard_monitor *monitor = (struct sc_clipboard_monitor *)param;
    
    // Đăng ký window class
    WNDCLASS wc = {0};
    wc.lpfnWndProc = clipboard_window_proc;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = CLIPBOARD_WINDOW_CLASS;
    
    if (!RegisterClass(&wc)) {
        LOGE("Could not register clipboard window class");
        return 1;
    }
    
    // Tạo invisible window để nhận clipboard messages
    HWND hwnd = CreateWindow(
        CLIPBOARD_WINDOW_CLASS,
        "Scrcpy Clipboard Monitor",
        0, 0, 0, 0, 0,
        HWND_MESSAGE, // Message-only window
        NULL,
        GetModuleHandle(NULL),
        NULL
    );
    
    if (!hwnd) {
        LOGE("Could not create clipboard monitor window");
        UnregisterClass(CLIPBOARD_WINDOW_CLASS, GetModuleHandle(NULL));
        return 1;
    }
    
    monitor->window_handle = hwnd;
    
    // Đăng ký để nhận clipboard change notifications
    if (!AddClipboardFormatListener(hwnd)) {
        LOGE("Could not add clipboard format listener");
        DestroyWindow(hwnd);
        UnregisterClass(CLIPBOARD_WINDOW_CLASS, GetModuleHandle(NULL));
        return 1;
    }
    
    LOGI("Clipboard monitoring started");
    monitor->monitoring = true;
    
    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0) > 0) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // Cleanup
    RemoveClipboardFormatListener(hwnd);
    DestroyWindow(hwnd);
    UnregisterClass(CLIPBOARD_WINDOW_CLASS, GetModuleHandle(NULL));
    
    monitor->monitoring = false;
    LOGI("Clipboard monitoring stopped");
    
    return 0;
}

#endif // _WIN32

bool
sc_clipboard_monitor_init(struct sc_clipboard_monitor *monitor,
                         struct sc_controller *controller) {
    assert(monitor);
    assert(controller);
    
    monitor->controller = controller;
    monitor->enabled = false;
    monitor->monitoring = false;
    
#ifdef _WIN32
    monitor->window_handle = NULL;
    monitor->thread_handle = NULL;
    monitor->thread_id = 0;
    g_monitor = monitor; // Set global reference
#endif
    
    return true;
}

bool
sc_clipboard_monitor_start(struct sc_clipboard_monitor *monitor) {
    assert(monitor);
    
    if (!sc_clipboard_monitor_is_supported()) {
        LOGW("Clipboard monitoring not supported on this platform");
        return false;
    }
    
    if (monitor->enabled) {
        LOGW("Clipboard monitor already started");
        return true;
    }
    
#ifdef _WIN32
    // Tạo thread để chạy clipboard monitoring
    monitor->thread_handle = CreateThread(
        NULL,
        0,
        clipboard_monitor_thread,
        monitor,
        0,
        &monitor->thread_id
    );
    
    if (!monitor->thread_handle) {
        LOGE("Could not create clipboard monitor thread");
        return false;
    }
    
    monitor->enabled = true;
    return true;
#else
    LOGW("Clipboard monitoring not implemented for this platform");
    return false;
#endif
}

void
sc_clipboard_monitor_stop(struct sc_clipboard_monitor *monitor) {
    assert(monitor);
    
    if (!monitor->enabled) {
        return;
    }
    
#ifdef _WIN32
    if (monitor->window_handle) {
        // Gửi WM_QUIT để dừng message loop
        PostMessage((HWND)monitor->window_handle, WM_QUIT, 0, 0);
    }
    
    if (monitor->thread_handle) {
        // Đợi thread kết thúc
        WaitForSingleObject(monitor->thread_handle, 5000); // 5 seconds timeout
        CloseHandle(monitor->thread_handle);
        monitor->thread_handle = NULL;
    }
#endif
    
    monitor->enabled = false;
}

void
sc_clipboard_monitor_destroy(struct sc_clipboard_monitor *monitor) {
    assert(monitor);
    
    sc_clipboard_monitor_stop(monitor);
    
#ifdef _WIN32
    if (g_monitor == monitor) {
        g_monitor = NULL;
    }
#endif
}

bool
sc_clipboard_monitor_is_supported(void) {
#ifdef _WIN32
    return true;
#else
    return false;
#endif
}
