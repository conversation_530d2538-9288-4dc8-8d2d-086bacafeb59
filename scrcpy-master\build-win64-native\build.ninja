# This is the build file for project "scrcpy"
# It is autogenerated by the Meson build system.
# Do not edit by hand.

ninja_required_version = 1.8.2

# Rules for module scanning.

# Rules for compiling.

rule c_COMPILER
 command = "cc" $ARGS -MD -MQ $out -MF $DEPFILE -o $out "-c" $in
 deps = gcc
 depfile = $DEPFILE_UNQUOTED
 description = Compiling C object $out

# Rules for linking.

rule c_LINKER
 command = "cc" $ARGS -o $out $in $LINK_ARGS
 description = Linking target $out

# Other rules

rule CUSTOM_COMMAND
 command = $COMMAND
 description = $DESC
 restat = 1

rule CUSTOM_COMMAND_DEP
 command = $COMMAND
 deps = gcc
 depfile = $DEPFILE_UNQUOTED
 description = $DESC
 restat = 1

rule REGENERATE_BUILD
 command = "C:/msys64/mingw64/bin/meson" "--internal" "regenerate" "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master" "."
 description = Regenerating build files
 generator = 1

# Phony build target, always out of date

build PHONY: phony 

# Build rules for targets

build app/app_scrcpy-windows.rc_scrcpy-windows.o: CUSTOM_COMMAND_DEP ../app/scrcpy-windows.rc | C$:/msys64/mingw64/bin/windres.EXE
 DEPFILE = "app/app_scrcpy-windows.rc_scrcpy-windows.o.d"
 DEPFILE_UNQUOTED = app/app_scrcpy-windows.rc_scrcpy-windows.o.d
 COMMAND = "C:\msys64\mingw64\bin/windres.EXE" "../app/scrcpy-windows.rc" "app/app_scrcpy-windows.rc_scrcpy-windows.o" "--preprocessor-arg=-MD" "--preprocessor-arg=-MQapp/app_scrcpy-windows.rc_scrcpy-windows.o" "--preprocessor-arg=-MFapp/app_scrcpy-windows.rc_scrcpy-windows.o.d"
 description = Compiling$ Windows$ resource$ app/scrcpy-windows.rc

build app/scrcpy.exe.p/src_main.c.obj: c_COMPILER ../app/src/main.c
 DEPFILE = "app/scrcpy.exe.p/src_main.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_main.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_adb_adb.c.obj: c_COMPILER ../app/src/adb/adb.c
 DEPFILE = "app/scrcpy.exe.p/src_adb_adb.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_adb_adb.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_adb_adb_device.c.obj: c_COMPILER ../app/src/adb/adb_device.c
 DEPFILE = "app/scrcpy.exe.p/src_adb_adb_device.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_adb_adb_device.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_adb_adb_parser.c.obj: c_COMPILER ../app/src/adb/adb_parser.c
 DEPFILE = "app/scrcpy.exe.p/src_adb_adb_parser.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_adb_adb_parser.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_adb_adb_tunnel.c.obj: c_COMPILER ../app/src/adb/adb_tunnel.c
 DEPFILE = "app/scrcpy.exe.p/src_adb_adb_tunnel.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_adb_adb_tunnel.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_audio_player.c.obj: c_COMPILER ../app/src/audio_player.c
 DEPFILE = "app/scrcpy.exe.p/src_audio_player.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_audio_player.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_audio_regulator.c.obj: c_COMPILER ../app/src/audio_regulator.c
 DEPFILE = "app/scrcpy.exe.p/src_audio_regulator.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_audio_regulator.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_cli.c.obj: c_COMPILER ../app/src/cli.c
 DEPFILE = "app/scrcpy.exe.p/src_cli.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_cli.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_clipboard_monitor.c.obj: c_COMPILER ../app/src/clipboard_monitor.c
 DEPFILE = "app/scrcpy.exe.p/src_clipboard_monitor.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_clipboard_monitor.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_clock.c.obj: c_COMPILER ../app/src/clock.c
 DEPFILE = "app/scrcpy.exe.p/src_clock.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_clock.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/scrcpy.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_control_msg.c.obj: c_COMPILER ../app/src/control_msg.c
 DEPFILE = "app/scrcpy.exe.p/src_control_msg.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_control_msg.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_controller.c.obj: c_COMPILER ../app/src/controller.c
 DEPFILE = "app/scrcpy.exe.p/src_controller.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_controller.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_decoder.c.obj: c_COMPILER ../app/src/decoder.c
 DEPFILE = "app/scrcpy.exe.p/src_decoder.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_decoder.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_delay_buffer.c.obj: c_COMPILER ../app/src/delay_buffer.c
 DEPFILE = "app/scrcpy.exe.p/src_delay_buffer.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_delay_buffer.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_demuxer.c.obj: c_COMPILER ../app/src/demuxer.c
 DEPFILE = "app/scrcpy.exe.p/src_demuxer.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_demuxer.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_device_msg.c.obj: c_COMPILER ../app/src/device_msg.c
 DEPFILE = "app/scrcpy.exe.p/src_device_msg.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_device_msg.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_display.c.obj: c_COMPILER ../app/src/display.c
 DEPFILE = "app/scrcpy.exe.p/src_display.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_display.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_events.c.obj: c_COMPILER ../app/src/events.c
 DEPFILE = "app/scrcpy.exe.p/src_events.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_events.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_icon.c.obj: c_COMPILER ../app/src/icon.c
 DEPFILE = "app/scrcpy.exe.p/src_icon.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_icon.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_file_pusher.c.obj: c_COMPILER ../app/src/file_pusher.c
 DEPFILE = "app/scrcpy.exe.p/src_file_pusher.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_file_pusher.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_fps_counter.c.obj: c_COMPILER ../app/src/fps_counter.c
 DEPFILE = "app/scrcpy.exe.p/src_fps_counter.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_fps_counter.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_frame_buffer.c.obj: c_COMPILER ../app/src/frame_buffer.c
 DEPFILE = "app/scrcpy.exe.p/src_frame_buffer.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_frame_buffer.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_input_manager.c.obj: c_COMPILER ../app/src/input_manager.c
 DEPFILE = "app/scrcpy.exe.p/src_input_manager.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_input_manager.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_keyboard_sdk.c.obj: c_COMPILER ../app/src/keyboard_sdk.c
 DEPFILE = "app/scrcpy.exe.p/src_keyboard_sdk.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_keyboard_sdk.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_mouse_capture.c.obj: c_COMPILER ../app/src/mouse_capture.c
 DEPFILE = "app/scrcpy.exe.p/src_mouse_capture.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_mouse_capture.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_mouse_sdk.c.obj: c_COMPILER ../app/src/mouse_sdk.c
 DEPFILE = "app/scrcpy.exe.p/src_mouse_sdk.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_mouse_sdk.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_opengl.c.obj: c_COMPILER ../app/src/opengl.c
 DEPFILE = "app/scrcpy.exe.p/src_opengl.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_opengl.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_options.c.obj: c_COMPILER ../app/src/options.c
 DEPFILE = "app/scrcpy.exe.p/src_options.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_options.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_packet_merger.c.obj: c_COMPILER ../app/src/packet_merger.c
 DEPFILE = "app/scrcpy.exe.p/src_packet_merger.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_packet_merger.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_receiver.c.obj: c_COMPILER ../app/src/receiver.c
 DEPFILE = "app/scrcpy.exe.p/src_receiver.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_receiver.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_recorder.c.obj: c_COMPILER ../app/src/recorder.c
 DEPFILE = "app/scrcpy.exe.p/src_recorder.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_recorder.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_scrcpy.c.obj: c_COMPILER ../app/src/scrcpy.c
 DEPFILE = "app/scrcpy.exe.p/src_scrcpy.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_scrcpy.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_screen.c.obj: c_COMPILER ../app/src/screen.c
 DEPFILE = "app/scrcpy.exe.p/src_screen.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_screen.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_server.c.obj: c_COMPILER ../app/src/server.c
 DEPFILE = "app/scrcpy.exe.p/src_server.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_server.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_version.c.obj: c_COMPILER ../app/src/version.c
 DEPFILE = "app/scrcpy.exe.p/src_version.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_version.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_hid_hid_gamepad.c.obj: c_COMPILER ../app/src/hid/hid_gamepad.c
 DEPFILE = "app/scrcpy.exe.p/src_hid_hid_gamepad.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_hid_hid_gamepad.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_hid_hid_keyboard.c.obj: c_COMPILER ../app/src/hid/hid_keyboard.c
 DEPFILE = "app/scrcpy.exe.p/src_hid_hid_keyboard.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_hid_hid_keyboard.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_hid_hid_mouse.c.obj: c_COMPILER ../app/src/hid/hid_mouse.c
 DEPFILE = "app/scrcpy.exe.p/src_hid_hid_mouse.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_hid_hid_mouse.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_trait_frame_source.c.obj: c_COMPILER ../app/src/trait/frame_source.c
 DEPFILE = "app/scrcpy.exe.p/src_trait_frame_source.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_trait_frame_source.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_trait_packet_source.c.obj: c_COMPILER ../app/src/trait/packet_source.c
 DEPFILE = "app/scrcpy.exe.p/src_trait_packet_source.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_trait_packet_source.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_uhid_gamepad_uhid.c.obj: c_COMPILER ../app/src/uhid/gamepad_uhid.c
 DEPFILE = "app/scrcpy.exe.p/src_uhid_gamepad_uhid.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_uhid_gamepad_uhid.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_uhid_keyboard_uhid.c.obj: c_COMPILER ../app/src/uhid/keyboard_uhid.c
 DEPFILE = "app/scrcpy.exe.p/src_uhid_keyboard_uhid.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_uhid_keyboard_uhid.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_uhid_mouse_uhid.c.obj: c_COMPILER ../app/src/uhid/mouse_uhid.c
 DEPFILE = "app/scrcpy.exe.p/src_uhid_mouse_uhid.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_uhid_mouse_uhid.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_uhid_uhid_output.c.obj: c_COMPILER ../app/src/uhid/uhid_output.c
 DEPFILE = "app/scrcpy.exe.p/src_uhid_uhid_output.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_uhid_uhid_output.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_acksync.c.obj: c_COMPILER ../app/src/util/acksync.c
 DEPFILE = "app/scrcpy.exe.p/src_util_acksync.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_acksync.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_audiobuf.c.obj: c_COMPILER ../app/src/util/audiobuf.c
 DEPFILE = "app/scrcpy.exe.p/src_util_audiobuf.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_audiobuf.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_average.c.obj: c_COMPILER ../app/src/util/average.c
 DEPFILE = "app/scrcpy.exe.p/src_util_average.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_average.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_env.c.obj: c_COMPILER ../app/src/util/env.c
 DEPFILE = "app/scrcpy.exe.p/src_util_env.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_env.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_file.c.obj: c_COMPILER ../app/src/util/file.c
 DEPFILE = "app/scrcpy.exe.p/src_util_file.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_file.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_intmap.c.obj: c_COMPILER ../app/src/util/intmap.c
 DEPFILE = "app/scrcpy.exe.p/src_util_intmap.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_intmap.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_intr.c.obj: c_COMPILER ../app/src/util/intr.c
 DEPFILE = "app/scrcpy.exe.p/src_util_intr.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_intr.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_log.c.obj: c_COMPILER ../app/src/util/log.c
 DEPFILE = "app/scrcpy.exe.p/src_util_log.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_log.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_memory.c.obj: c_COMPILER ../app/src/util/memory.c
 DEPFILE = "app/scrcpy.exe.p/src_util_memory.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_memory.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_net.c.obj: c_COMPILER ../app/src/util/net.c
 DEPFILE = "app/scrcpy.exe.p/src_util_net.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_net.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_net_intr.c.obj: c_COMPILER ../app/src/util/net_intr.c
 DEPFILE = "app/scrcpy.exe.p/src_util_net_intr.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_net_intr.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_process.c.obj: c_COMPILER ../app/src/util/process.c
 DEPFILE = "app/scrcpy.exe.p/src_util_process.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_process.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_process_intr.c.obj: c_COMPILER ../app/src/util/process_intr.c
 DEPFILE = "app/scrcpy.exe.p/src_util_process_intr.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_process_intr.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_rand.c.obj: c_COMPILER ../app/src/util/rand.c
 DEPFILE = "app/scrcpy.exe.p/src_util_rand.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_rand.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_strbuf.c.obj: c_COMPILER ../app/src/util/strbuf.c
 DEPFILE = "app/scrcpy.exe.p/src_util_strbuf.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_strbuf.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_str.c.obj: c_COMPILER ../app/src/util/str.c
 DEPFILE = "app/scrcpy.exe.p/src_util_str.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_str.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_term.c.obj: c_COMPILER ../app/src/util/term.c
 DEPFILE = "app/scrcpy.exe.p/src_util_term.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_term.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_thread.c.obj: c_COMPILER ../app/src/util/thread.c
 DEPFILE = "app/scrcpy.exe.p/src_util_thread.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_thread.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_tick.c.obj: c_COMPILER ../app/src/util/tick.c
 DEPFILE = "app/scrcpy.exe.p/src_util_tick.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_tick.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_util_timeout.c.obj: c_COMPILER ../app/src/util/timeout.c
 DEPFILE = "app/scrcpy.exe.p/src_util_timeout.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_util_timeout.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_sys_win_file.c.obj: c_COMPILER ../app/src/sys/win/file.c
 DEPFILE = "app/scrcpy.exe.p/src_sys_win_file.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_sys_win_file.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_sys_win_process.c.obj: c_COMPILER ../app/src/sys/win/process.c
 DEPFILE = "app/scrcpy.exe.p/src_sys_win_process.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_sys_win_process.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_usb_aoa_hid.c.obj: c_COMPILER ../app/src/usb/aoa_hid.c
 DEPFILE = "app/scrcpy.exe.p/src_usb_aoa_hid.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_usb_aoa_hid.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_usb_gamepad_aoa.c.obj: c_COMPILER ../app/src/usb/gamepad_aoa.c
 DEPFILE = "app/scrcpy.exe.p/src_usb_gamepad_aoa.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_usb_gamepad_aoa.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_usb_keyboard_aoa.c.obj: c_COMPILER ../app/src/usb/keyboard_aoa.c
 DEPFILE = "app/scrcpy.exe.p/src_usb_keyboard_aoa.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_usb_keyboard_aoa.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_usb_mouse_aoa.c.obj: c_COMPILER ../app/src/usb/mouse_aoa.c
 DEPFILE = "app/scrcpy.exe.p/src_usb_mouse_aoa.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_usb_mouse_aoa.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_usb_scrcpy_otg.c.obj: c_COMPILER ../app/src/usb/scrcpy_otg.c
 DEPFILE = "app/scrcpy.exe.p/src_usb_scrcpy_otg.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_usb_scrcpy_otg.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_usb_screen_otg.c.obj: c_COMPILER ../app/src/usb/screen_otg.c
 DEPFILE = "app/scrcpy.exe.p/src_usb_screen_otg.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_usb_screen_otg.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe.p/src_usb_usb.c.obj: c_COMPILER ../app/src/usb/usb.c
 DEPFILE = "app/scrcpy.exe.p/src_usb_usb.c.obj.d"
 DEPFILE_UNQUOTED = app/scrcpy.exe.p/src_usb_usb.c.obj.d
 ARGS = "-Iapp/scrcpy.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main"

build app/scrcpy.exe: c_LINKER app/app_scrcpy-windows.rc_scrcpy-windows.o app/scrcpy.exe.p/src_main.c.obj app/scrcpy.exe.p/src_adb_adb.c.obj app/scrcpy.exe.p/src_adb_adb_device.c.obj app/scrcpy.exe.p/src_adb_adb_parser.c.obj app/scrcpy.exe.p/src_adb_adb_tunnel.c.obj app/scrcpy.exe.p/src_audio_player.c.obj app/scrcpy.exe.p/src_audio_regulator.c.obj app/scrcpy.exe.p/src_cli.c.obj app/scrcpy.exe.p/src_clipboard_monitor.c.obj app/scrcpy.exe.p/src_clock.c.obj app/scrcpy.exe.p/src_compat.c.obj app/scrcpy.exe.p/src_control_msg.c.obj app/scrcpy.exe.p/src_controller.c.obj app/scrcpy.exe.p/src_decoder.c.obj app/scrcpy.exe.p/src_delay_buffer.c.obj app/scrcpy.exe.p/src_demuxer.c.obj app/scrcpy.exe.p/src_device_msg.c.obj app/scrcpy.exe.p/src_display.c.obj app/scrcpy.exe.p/src_events.c.obj app/scrcpy.exe.p/src_icon.c.obj app/scrcpy.exe.p/src_file_pusher.c.obj app/scrcpy.exe.p/src_fps_counter.c.obj app/scrcpy.exe.p/src_frame_buffer.c.obj app/scrcpy.exe.p/src_input_manager.c.obj app/scrcpy.exe.p/src_keyboard_sdk.c.obj app/scrcpy.exe.p/src_mouse_capture.c.obj app/scrcpy.exe.p/src_mouse_sdk.c.obj app/scrcpy.exe.p/src_opengl.c.obj app/scrcpy.exe.p/src_options.c.obj app/scrcpy.exe.p/src_packet_merger.c.obj app/scrcpy.exe.p/src_receiver.c.obj app/scrcpy.exe.p/src_recorder.c.obj app/scrcpy.exe.p/src_scrcpy.c.obj app/scrcpy.exe.p/src_screen.c.obj app/scrcpy.exe.p/src_server.c.obj app/scrcpy.exe.p/src_version.c.obj app/scrcpy.exe.p/src_hid_hid_gamepad.c.obj app/scrcpy.exe.p/src_hid_hid_keyboard.c.obj app/scrcpy.exe.p/src_hid_hid_mouse.c.obj app/scrcpy.exe.p/src_trait_frame_source.c.obj app/scrcpy.exe.p/src_trait_packet_source.c.obj app/scrcpy.exe.p/src_uhid_gamepad_uhid.c.obj app/scrcpy.exe.p/src_uhid_keyboard_uhid.c.obj app/scrcpy.exe.p/src_uhid_mouse_uhid.c.obj app/scrcpy.exe.p/src_uhid_uhid_output.c.obj app/scrcpy.exe.p/src_util_acksync.c.obj app/scrcpy.exe.p/src_util_audiobuf.c.obj app/scrcpy.exe.p/src_util_average.c.obj app/scrcpy.exe.p/src_util_env.c.obj app/scrcpy.exe.p/src_util_file.c.obj app/scrcpy.exe.p/src_util_intmap.c.obj app/scrcpy.exe.p/src_util_intr.c.obj app/scrcpy.exe.p/src_util_log.c.obj app/scrcpy.exe.p/src_util_memory.c.obj app/scrcpy.exe.p/src_util_net.c.obj app/scrcpy.exe.p/src_util_net_intr.c.obj app/scrcpy.exe.p/src_util_process.c.obj app/scrcpy.exe.p/src_util_process_intr.c.obj app/scrcpy.exe.p/src_util_rand.c.obj app/scrcpy.exe.p/src_util_strbuf.c.obj app/scrcpy.exe.p/src_util_str.c.obj app/scrcpy.exe.p/src_util_term.c.obj app/scrcpy.exe.p/src_util_thread.c.obj app/scrcpy.exe.p/src_util_tick.c.obj app/scrcpy.exe.p/src_util_timeout.c.obj app/scrcpy.exe.p/src_sys_win_file.c.obj app/scrcpy.exe.p/src_sys_win_process.c.obj app/scrcpy.exe.p/src_usb_aoa_hid.c.obj app/scrcpy.exe.p/src_usb_gamepad_aoa.c.obj app/scrcpy.exe.p/src_usb_keyboard_aoa.c.obj app/scrcpy.exe.p/src_usb_mouse_aoa.c.obj app/scrcpy.exe.p/src_usb_scrcpy_otg.c.obj app/scrcpy.exe.p/src_usb_screen_otg.c.obj app/scrcpy.exe.p/src_usb_usb.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_adb_parser.exe.p/tests_test_adb_parser.c.obj: c_COMPILER ../app/tests/test_adb_parser.c
 DEPFILE = "app/test_adb_parser.exe.p/tests_test_adb_parser.c.obj.d"
 DEPFILE_UNQUOTED = app/test_adb_parser.exe.p/tests_test_adb_parser.c.obj.d
 ARGS = "-Iapp/test_adb_parser.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_adb_parser.exe.p/src_adb_adb_device.c.obj: c_COMPILER ../app/src/adb/adb_device.c
 DEPFILE = "app/test_adb_parser.exe.p/src_adb_adb_device.c.obj.d"
 DEPFILE_UNQUOTED = app/test_adb_parser.exe.p/src_adb_adb_device.c.obj.d
 ARGS = "-Iapp/test_adb_parser.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_adb_parser.exe.p/src_adb_adb_parser.c.obj: c_COMPILER ../app/src/adb/adb_parser.c
 DEPFILE = "app/test_adb_parser.exe.p/src_adb_adb_parser.c.obj.d"
 DEPFILE_UNQUOTED = app/test_adb_parser.exe.p/src_adb_adb_parser.c.obj.d
 ARGS = "-Iapp/test_adb_parser.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_adb_parser.exe.p/src_util_str.c.obj: c_COMPILER ../app/src/util/str.c
 DEPFILE = "app/test_adb_parser.exe.p/src_util_str.c.obj.d"
 DEPFILE_UNQUOTED = app/test_adb_parser.exe.p/src_util_str.c.obj.d
 ARGS = "-Iapp/test_adb_parser.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_adb_parser.exe.p/src_util_strbuf.c.obj: c_COMPILER ../app/src/util/strbuf.c
 DEPFILE = "app/test_adb_parser.exe.p/src_util_strbuf.c.obj.d"
 DEPFILE_UNQUOTED = app/test_adb_parser.exe.p/src_util_strbuf.c.obj.d
 ARGS = "-Iapp/test_adb_parser.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_adb_parser.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_adb_parser.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_adb_parser.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_adb_parser.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_adb_parser.exe: c_LINKER app/test_adb_parser.exe.p/tests_test_adb_parser.c.obj app/test_adb_parser.exe.p/src_adb_adb_device.c.obj app/test_adb_parser.exe.p/src_adb_adb_parser.c.obj app/test_adb_parser.exe.p/src_util_str.c.obj app/test_adb_parser.exe.p/src_util_strbuf.c.obj app/test_adb_parser.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_binary.exe.p/tests_test_binary.c.obj: c_COMPILER ../app/tests/test_binary.c
 DEPFILE = "app/test_binary.exe.p/tests_test_binary.c.obj.d"
 DEPFILE_UNQUOTED = app/test_binary.exe.p/tests_test_binary.c.obj.d
 ARGS = "-Iapp/test_binary.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_binary.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_binary.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_binary.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_binary.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_binary.exe: c_LINKER app/test_binary.exe.p/tests_test_binary.c.obj app/test_binary.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_audiobuf.exe.p/tests_test_audiobuf.c.obj: c_COMPILER ../app/tests/test_audiobuf.c
 DEPFILE = "app/test_audiobuf.exe.p/tests_test_audiobuf.c.obj.d"
 DEPFILE_UNQUOTED = app/test_audiobuf.exe.p/tests_test_audiobuf.c.obj.d
 ARGS = "-Iapp/test_audiobuf.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_audiobuf.exe.p/src_util_audiobuf.c.obj: c_COMPILER ../app/src/util/audiobuf.c
 DEPFILE = "app/test_audiobuf.exe.p/src_util_audiobuf.c.obj.d"
 DEPFILE_UNQUOTED = app/test_audiobuf.exe.p/src_util_audiobuf.c.obj.d
 ARGS = "-Iapp/test_audiobuf.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_audiobuf.exe.p/src_util_memory.c.obj: c_COMPILER ../app/src/util/memory.c
 DEPFILE = "app/test_audiobuf.exe.p/src_util_memory.c.obj.d"
 DEPFILE_UNQUOTED = app/test_audiobuf.exe.p/src_util_memory.c.obj.d
 ARGS = "-Iapp/test_audiobuf.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_audiobuf.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_audiobuf.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_audiobuf.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_audiobuf.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_audiobuf.exe: c_LINKER app/test_audiobuf.exe.p/tests_test_audiobuf.c.obj app/test_audiobuf.exe.p/src_util_audiobuf.c.obj app/test_audiobuf.exe.p/src_util_memory.c.obj app/test_audiobuf.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_cli.exe.p/tests_test_cli.c.obj: c_COMPILER ../app/tests/test_cli.c
 DEPFILE = "app/test_cli.exe.p/tests_test_cli.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/tests_test_cli.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe.p/src_cli.c.obj: c_COMPILER ../app/src/cli.c
 DEPFILE = "app/test_cli.exe.p/src_cli.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/src_cli.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe.p/src_options.c.obj: c_COMPILER ../app/src/options.c
 DEPFILE = "app/test_cli.exe.p/src_options.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/src_options.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe.p/src_util_log.c.obj: c_COMPILER ../app/src/util/log.c
 DEPFILE = "app/test_cli.exe.p/src_util_log.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/src_util_log.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe.p/src_util_net.c.obj: c_COMPILER ../app/src/util/net.c
 DEPFILE = "app/test_cli.exe.p/src_util_net.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/src_util_net.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe.p/src_util_str.c.obj: c_COMPILER ../app/src/util/str.c
 DEPFILE = "app/test_cli.exe.p/src_util_str.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/src_util_str.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe.p/src_util_strbuf.c.obj: c_COMPILER ../app/src/util/strbuf.c
 DEPFILE = "app/test_cli.exe.p/src_util_strbuf.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/src_util_strbuf.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe.p/src_util_term.c.obj: c_COMPILER ../app/src/util/term.c
 DEPFILE = "app/test_cli.exe.p/src_util_term.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/src_util_term.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_cli.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_cli.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_cli.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_cli.exe: c_LINKER app/test_cli.exe.p/tests_test_cli.c.obj app/test_cli.exe.p/src_cli.c.obj app/test_cli.exe.p/src_options.c.obj app/test_cli.exe.p/src_util_log.c.obj app/test_cli.exe.p/src_util_net.c.obj app/test_cli.exe.p/src_util_str.c.obj app/test_cli.exe.p/src_util_strbuf.c.obj app/test_cli.exe.p/src_util_term.c.obj app/test_cli.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_control_msg_serialize.exe.p/tests_test_control_msg_serialize.c.obj: c_COMPILER ../app/tests/test_control_msg_serialize.c
 DEPFILE = "app/test_control_msg_serialize.exe.p/tests_test_control_msg_serialize.c.obj.d"
 DEPFILE_UNQUOTED = app/test_control_msg_serialize.exe.p/tests_test_control_msg_serialize.c.obj.d
 ARGS = "-Iapp/test_control_msg_serialize.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_control_msg_serialize.exe.p/src_control_msg.c.obj: c_COMPILER ../app/src/control_msg.c
 DEPFILE = "app/test_control_msg_serialize.exe.p/src_control_msg.c.obj.d"
 DEPFILE_UNQUOTED = app/test_control_msg_serialize.exe.p/src_control_msg.c.obj.d
 ARGS = "-Iapp/test_control_msg_serialize.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_control_msg_serialize.exe.p/src_util_str.c.obj: c_COMPILER ../app/src/util/str.c
 DEPFILE = "app/test_control_msg_serialize.exe.p/src_util_str.c.obj.d"
 DEPFILE_UNQUOTED = app/test_control_msg_serialize.exe.p/src_util_str.c.obj.d
 ARGS = "-Iapp/test_control_msg_serialize.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_control_msg_serialize.exe.p/src_util_strbuf.c.obj: c_COMPILER ../app/src/util/strbuf.c
 DEPFILE = "app/test_control_msg_serialize.exe.p/src_util_strbuf.c.obj.d"
 DEPFILE_UNQUOTED = app/test_control_msg_serialize.exe.p/src_util_strbuf.c.obj.d
 ARGS = "-Iapp/test_control_msg_serialize.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_control_msg_serialize.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_control_msg_serialize.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_control_msg_serialize.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_control_msg_serialize.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_control_msg_serialize.exe: c_LINKER app/test_control_msg_serialize.exe.p/tests_test_control_msg_serialize.c.obj app/test_control_msg_serialize.exe.p/src_control_msg.c.obj app/test_control_msg_serialize.exe.p/src_util_str.c.obj app/test_control_msg_serialize.exe.p/src_util_strbuf.c.obj app/test_control_msg_serialize.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_device_msg_deserialize.exe.p/tests_test_device_msg_deserialize.c.obj: c_COMPILER ../app/tests/test_device_msg_deserialize.c
 DEPFILE = "app/test_device_msg_deserialize.exe.p/tests_test_device_msg_deserialize.c.obj.d"
 DEPFILE_UNQUOTED = app/test_device_msg_deserialize.exe.p/tests_test_device_msg_deserialize.c.obj.d
 ARGS = "-Iapp/test_device_msg_deserialize.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_device_msg_deserialize.exe.p/src_device_msg.c.obj: c_COMPILER ../app/src/device_msg.c
 DEPFILE = "app/test_device_msg_deserialize.exe.p/src_device_msg.c.obj.d"
 DEPFILE_UNQUOTED = app/test_device_msg_deserialize.exe.p/src_device_msg.c.obj.d
 ARGS = "-Iapp/test_device_msg_deserialize.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_device_msg_deserialize.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_device_msg_deserialize.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_device_msg_deserialize.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_device_msg_deserialize.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_device_msg_deserialize.exe: c_LINKER app/test_device_msg_deserialize.exe.p/tests_test_device_msg_deserialize.c.obj app/test_device_msg_deserialize.exe.p/src_device_msg.c.obj app/test_device_msg_deserialize.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_orientation.exe.p/tests_test_orientation.c.obj: c_COMPILER ../app/tests/test_orientation.c
 DEPFILE = "app/test_orientation.exe.p/tests_test_orientation.c.obj.d"
 DEPFILE_UNQUOTED = app/test_orientation.exe.p/tests_test_orientation.c.obj.d
 ARGS = "-Iapp/test_orientation.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_orientation.exe.p/src_options.c.obj: c_COMPILER ../app/src/options.c
 DEPFILE = "app/test_orientation.exe.p/src_options.c.obj.d"
 DEPFILE_UNQUOTED = app/test_orientation.exe.p/src_options.c.obj.d
 ARGS = "-Iapp/test_orientation.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_orientation.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_orientation.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_orientation.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_orientation.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_orientation.exe: c_LINKER app/test_orientation.exe.p/tests_test_orientation.c.obj app/test_orientation.exe.p/src_options.c.obj app/test_orientation.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_strbuf.exe.p/tests_test_strbuf.c.obj: c_COMPILER ../app/tests/test_strbuf.c
 DEPFILE = "app/test_strbuf.exe.p/tests_test_strbuf.c.obj.d"
 DEPFILE_UNQUOTED = app/test_strbuf.exe.p/tests_test_strbuf.c.obj.d
 ARGS = "-Iapp/test_strbuf.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_strbuf.exe.p/src_util_strbuf.c.obj: c_COMPILER ../app/src/util/strbuf.c
 DEPFILE = "app/test_strbuf.exe.p/src_util_strbuf.c.obj.d"
 DEPFILE_UNQUOTED = app/test_strbuf.exe.p/src_util_strbuf.c.obj.d
 ARGS = "-Iapp/test_strbuf.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_strbuf.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_strbuf.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_strbuf.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_strbuf.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_strbuf.exe: c_LINKER app/test_strbuf.exe.p/tests_test_strbuf.c.obj app/test_strbuf.exe.p/src_util_strbuf.c.obj app/test_strbuf.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_str.exe.p/tests_test_str.c.obj: c_COMPILER ../app/tests/test_str.c
 DEPFILE = "app/test_str.exe.p/tests_test_str.c.obj.d"
 DEPFILE_UNQUOTED = app/test_str.exe.p/tests_test_str.c.obj.d
 ARGS = "-Iapp/test_str.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_str.exe.p/src_util_str.c.obj: c_COMPILER ../app/src/util/str.c
 DEPFILE = "app/test_str.exe.p/src_util_str.c.obj.d"
 DEPFILE_UNQUOTED = app/test_str.exe.p/src_util_str.c.obj.d
 ARGS = "-Iapp/test_str.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_str.exe.p/src_util_strbuf.c.obj: c_COMPILER ../app/src/util/strbuf.c
 DEPFILE = "app/test_str.exe.p/src_util_strbuf.c.obj.d"
 DEPFILE_UNQUOTED = app/test_str.exe.p/src_util_strbuf.c.obj.d
 ARGS = "-Iapp/test_str.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_str.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_str.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_str.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_str.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_str.exe: c_LINKER app/test_str.exe.p/tests_test_str.c.obj app/test_str.exe.p/src_util_str.c.obj app/test_str.exe.p/src_util_strbuf.c.obj app/test_str.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_vecdeque.exe.p/tests_test_vecdeque.c.obj: c_COMPILER ../app/tests/test_vecdeque.c
 DEPFILE = "app/test_vecdeque.exe.p/tests_test_vecdeque.c.obj.d"
 DEPFILE_UNQUOTED = app/test_vecdeque.exe.p/tests_test_vecdeque.c.obj.d
 ARGS = "-Iapp/test_vecdeque.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_vecdeque.exe.p/src_util_memory.c.obj: c_COMPILER ../app/src/util/memory.c
 DEPFILE = "app/test_vecdeque.exe.p/src_util_memory.c.obj.d"
 DEPFILE_UNQUOTED = app/test_vecdeque.exe.p/src_util_memory.c.obj.d
 ARGS = "-Iapp/test_vecdeque.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_vecdeque.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_vecdeque.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_vecdeque.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_vecdeque.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_vecdeque.exe: c_LINKER app/test_vecdeque.exe.p/tests_test_vecdeque.c.obj app/test_vecdeque.exe.p/src_util_memory.c.obj app/test_vecdeque.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build app/test_vector.exe.p/tests_test_vector.c.obj: c_COMPILER ../app/tests/test_vector.c
 DEPFILE = "app/test_vector.exe.p/tests_test_vector.c.obj.d"
 DEPFILE_UNQUOTED = app/test_vector.exe.p/tests_test_vector.c.obj.d
 ARGS = "-Iapp/test_vector.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_vector.exe.p/src_compat.c.obj: c_COMPILER ../app/src/compat.c
 DEPFILE = "app/test_vector.exe.p/src_compat.c.obj.d"
 DEPFILE_UNQUOTED = app/test_vector.exe.p/src_compat.c.obj.d
 ARGS = "-Iapp/test_vector.exe.p" "-Iapp" "-I../app" "-I../app/src" "-IC:/msys64/mingw64/include/SDL2" "-IC:/msys64/mingw64/include/libusb-1.0" "-fdiagnostics-color=always" "-D_FILE_OFFSET_BITS=64" "-Wall" "-Winvalid-pch" "-Wextra" "-std=c11" "-O0" "-g" "-Wmissing-prototypes" "-Dmain=SDL_main" "-DSDL_MAIN_HANDLED" "-DSC_TEST"

build app/test_vector.exe: c_LINKER app/test_vector.exe.p/tests_test_vector.c.obj app/test_vector.exe.p/src_compat.c.obj | C$:/msys64/mingw64/lib/libSDL2.dll.a C$:/msys64/mingw64/lib/libSDL2main.a C$:/msys64/mingw64/lib/libadvapi32.a C$:/msys64/mingw64/lib/libavcodec.dll.a C$:/msys64/mingw64/lib/libavformat.dll.a C$:/msys64/mingw64/lib/libavutil.dll.a C$:/msys64/mingw64/lib/libcomdlg32.a C$:/msys64/mingw64/lib/libgdi32.a C$:/msys64/mingw64/lib/libkernel32.a C$:/msys64/mingw64/lib/libmingw32.a C$:/msys64/mingw64/lib/libole32.a C$:/msys64/mingw64/lib/liboleaut32.a C$:/msys64/mingw64/lib/libshell32.a C$:/msys64/mingw64/lib/libswresample.dll.a C$:/msys64/mingw64/lib/libusb-1.0.dll.a C$:/msys64/mingw64/lib/libuser32.a C$:/msys64/mingw64/lib/libuuid.a C$:/msys64/mingw64/lib/libwinspool.a C$:/msys64/mingw64/lib/libws2_32.a
 LINK_ARGS = "-Wl,--allow-shlib-undefined" "-Wl,--start-group" "C:/msys64/mingw64/lib/libavformat.dll.a" "C:/msys64/mingw64/lib/libavcodec.dll.a" "C:/msys64/mingw64/lib/libavutil.dll.a" "C:/msys64/mingw64/lib/libswresample.dll.a" "C:/msys64/mingw64/lib/libmingw32.a" "-mwindows" "C:/msys64/mingw64/lib/libSDL2main.a" "C:/msys64/mingw64/lib/libSDL2.dll.a" "C:/msys64/mingw64/lib/libusb-1.0.dll.a" "-lmingw32" "-lws2_32" "-Wl,--subsystem,console" "-lkernel32" "-luser32" "-lgdi32" "-lwinspool" "-lshell32" "-lole32" "-loleaut32" "-luuid" "-lcomdlg32" "-ladvapi32" "-Wl,--end-group"

build server/scrcpy-server: CUSTOM_COMMAND  | C$:/Users/<USER>/Pictures/scrcpy/scrcpy-master/server/./scripts/build-wrapper.sh PHONY
 pool = console
 COMMAND = "bash" "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/server/./scripts/build-wrapper.sh" "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/server" "server/scrcpy-server" "debug"
 description = Generating$ server/scrcpy-server$ with$ a$ custom$ command

# Test rules

build test: phony meson-internal__test

build meson-internal__test: CUSTOM_COMMAND all meson-test-prereq PHONY
 COMMAND = "C:/msys64/mingw64/bin/meson" "test" "--no-rebuild" "--print-errorlogs"
 DESC = Running$ all$ tests
 pool = console

build benchmark: phony meson-internal__benchmark

build meson-internal__benchmark: CUSTOM_COMMAND all meson-benchmark-prereq PHONY
 COMMAND = "C:/msys64/mingw64/bin/meson" "test" "--benchmark" "--logbase" "benchmarklog" "--num-processes=1" "--no-rebuild"
 DESC = Running$ benchmark$ suite
 pool = console

# Install rules

build install: phony meson-internal__install

build meson-internal__install: CUSTOM_COMMAND PHONY | all
 DESC = Installing$ files
 COMMAND = "C:/msys64/mingw64/bin/meson" "install" "--no-rebuild"
 pool = console

build dist: phony meson-internal__dist

build meson-internal__dist: CUSTOM_COMMAND PHONY
 DESC = Creating$ source$ packages
 COMMAND = "C:/msys64/mingw64/bin/meson" "dist"
 pool = console

# Suffix

build uninstall: phony meson-internal__uninstall

build meson-internal__uninstall: CUSTOM_COMMAND PHONY
 COMMAND = "C:/msys64/mingw64/bin/meson" "--internal" "uninstall"
 pool = console

build all: phony app/scrcpy.exe app/test_adb_parser.exe app/test_binary.exe app/test_audiobuf.exe app/test_cli.exe app/test_control_msg_serialize.exe app/test_device_msg_deserialize.exe app/test_orientation.exe app/test_strbuf.exe app/test_str.exe app/test_vecdeque.exe app/test_vector.exe server/scrcpy-server

build meson-test-prereq: phony app/test_adb_parser.exe app/test_binary.exe app/test_audiobuf.exe app/test_cli.exe app/test_control_msg_serialize.exe app/test_device_msg_deserialize.exe app/test_orientation.exe app/test_strbuf.exe app/test_str.exe app/test_vecdeque.exe app/test_vector.exe

build meson-benchmark-prereq: phony 

build clean: phony meson-internal__clean

build clean-ctlist: phony meson-internal__clean-ctlist

build meson-internal__clean-ctlist: CUSTOM_COMMAND PHONY
 COMMAND = "C:/msys64/mingw64/bin/meson" "--internal" "cleantrees" "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cleantrees.dat"
 description = Cleaning$ custom$ target$ directories

build meson-internal__clean: CUSTOM_COMMAND PHONY | clean-ctlist
 COMMAND = "C:\msys64\mingw64\bin/ninja.EXE" "-t" "clean"
 description = Cleaning

build build.ninja: REGENERATE_BUILD ../meson.build C$:/Users/<USER>/Pictures/scrcpy/scrcpy-master/meson_options.txt ../app/meson.build ../server/meson.build meson-private/coredata.dat
 pool = console

build reconfigure: REGENERATE_BUILD PHONY
 pool = console

build ../meson.build C$:/Users/<USER>/Pictures/scrcpy/scrcpy-master/meson_options.txt ../app/meson.build ../server/meson.build meson-private/coredata.dat: phony 

default all

