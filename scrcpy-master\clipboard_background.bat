@echo off
REM Chạy scrcpy clipboard sync trong background

echo ========================================
echo Scrcpy Background Clipboard Service
echo ========================================
echo.

REM Thêm ADB vào PATH nếu cần
if exist "D:\Android\GT_Neo2\platform-tools\adb.exe" (
    set "PATH=%PATH%;D:\Android\GT_Neo2\platform-tools"
)

echo Đang khởi động clipboard sync trong background...
echo.

REM Chạy scrcpy với window nhỏ trong background
start /min "Scrcpy Clipboard" build-win64-native\app\scrcpy.exe --clipboard-monitor --window-width=320 --window-height=240 --no-audio --window-title="Clipboard Sync"

echo ✅ Background service đã được khởi động!
echo.
echo Tính năng:
echo - Bidirectional clipboard sync
echo - <PERSON><PERSON>y với window nhỏ (320x240)
echo - Không có audio để tiết kiệm tài nguyên
echo.
echo Để dừng service:
echo - Tìm window "Clipboard Sync" trong taskbar và đóng
echo - Hoặc dùng Task Manager để kill process scrcpy.exe
echo.
pause
