/*
 * Demo clipboard monitoring cho Windows
 * Compile với: gcc -o clipboard_test_demo.exe clipboard_test_demo.c -luser32
 */

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>

static const char *WINDOW_CLASS = "ClipboardTestDemo";
static HWND g_hwnd = NULL;
static BOOL g_monitoring = FALSE;

// Forward declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
void PrintClipboardText(void);

LRESULT CALLBACK WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    switch (msg) {
        case WM_CLIPBOARDUPDATE:
            printf("=== Clipboard Updated ===\n");
            PrintClipboardText();
            printf("========================\n\n");
            return 0;
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

void PrintClipboardText(void) {
    if (!OpenClipboard(g_hwnd)) {
        printf("ERROR: Could not open clipboard\n");
        return;
    }

    HANDLE hData = GetClipboardData(CF_TEXT);
    if (hData == NULL) {
        printf("No text data in clipboard\n");
        CloseClipboard();
        return;
    }

    char *text = (char*)GlobalLock(hData);
    if (text == NULL) {
        printf("ERROR: Could not lock clipboard data\n");
        CloseClipboard();
        return;
    }

    printf("Clipboard text: \"%s\"\n", text);
    printf("Length: %zu characters\n", strlen(text));
    
    // Trong scrcpy thực tế, đây là nơi chúng ta sẽ gửi text tới Android device
    printf(">>> Would send to Android device <<<\n");

    GlobalUnlock(hData);
    CloseClipboard();
}

int main() {
    printf("=== Scrcpy Clipboard Monitoring Demo ===\n");
    printf("This demo shows how clipboard monitoring works\n");
    printf("Copy text to see automatic detection\n");
    printf("Press Ctrl+C to quit\n\n");

    // Register window class
    WNDCLASS wc = {0};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = WINDOW_CLASS;

    if (!RegisterClass(&wc)) {
        printf("ERROR: Could not register window class\n");
        return 1;
    }

    // Create invisible window
    g_hwnd = CreateWindow(
        WINDOW_CLASS,
        "Clipboard Monitor Demo",
        0, 0, 0, 0, 0,
        HWND_MESSAGE, // Message-only window
        NULL,
        GetModuleHandle(NULL),
        NULL
    );

    if (!g_hwnd) {
        printf("ERROR: Could not create window\n");
        UnregisterClass(WINDOW_CLASS, GetModuleHandle(NULL));
        return 1;
    }

    // Register for clipboard notifications
    if (!AddClipboardFormatListener(g_hwnd)) {
        printf("ERROR: Could not add clipboard format listener\n");
        DestroyWindow(g_hwnd);
        UnregisterClass(WINDOW_CLASS, GetModuleHandle(NULL));
        return 1;
    }

    printf("Clipboard monitoring started successfully!\n");
    printf("Try copying some text...\n\n");
    g_monitoring = TRUE;

    // Show current clipboard content
    printf("Current clipboard content:\n");
    PrintClipboardText();
    printf("\n");

    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0) > 0) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // Cleanup
    RemoveClipboardFormatListener(g_hwnd);
    DestroyWindow(g_hwnd);
    UnregisterClass(WINDOW_CLASS, GetModuleHandle(NULL));

    printf("Clipboard monitoring stopped\n");
    return 0;
}

/*
 * Compile instructions:
 * 
 * MINGW64:
 *   gcc -o clipboard_test_demo.exe clipboard_test_demo.c -luser32
 * 
 * Visual Studio:
 *   cl clipboard_test_demo.c user32.lib
 * 
 * Usage:
 *   ./clipboard_test_demo.exe
 *   
 * Then copy text from anywhere (Ctrl+C, right-click copy, etc.)
 * and see the automatic detection in console.
 * 
 * This demonstrates the core clipboard monitoring functionality
 * that has been integrated into scrcpy.
 */
