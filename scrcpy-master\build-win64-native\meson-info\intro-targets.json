[{"name": "scrcpy-windows.rc", "id": "a172ced@@scrcpy-windows.rc@cus", "type": "custom", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/app_scrcpy-windows.rc_scrcpy-windows.o"], "build_by_default": false, "target_sources": [{"language": "unknown", "compiler": ["C:\\msys64\\mingw64\\bin/windres.EXE", "@INPUT@", "@OUTPUT@", "--preprocessor-arg=-MD", "--preprocessor-arg=-MQ@OUTPUT@", "--preprocessor-arg=-MF@DEPFILE@"], "parameters": [], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/scrcpy-windows.rc"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "scrcpy", "id": "a172ced@@scrcpy@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/scrcpy.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/scrcpy.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/main.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/adb/adb.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/adb/adb_device.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/adb/adb_parser.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/adb/adb_tunnel.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/audio_player.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/audio_regulator.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/cli.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/clipboard_monitor.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/clock.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/control_msg.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/controller.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/decoder.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/delay_buffer.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/demuxer.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/device_msg.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/display.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/events.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/icon.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/file_pusher.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/fps_counter.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/frame_buffer.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/input_manager.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/keyboard_sdk.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/mouse_capture.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/mouse_sdk.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/opengl.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/options.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/packet_merger.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/receiver.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/recorder.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/scrcpy.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/screen.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/server.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/version.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/hid/hid_gamepad.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/hid/hid_keyboard.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/hid/hid_mouse.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/trait/frame_source.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/trait/packet_source.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/uhid/gamepad_uhid.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/uhid/keyboard_uhid.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/uhid/mouse_uhid.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/uhid/uhid_output.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/acksync.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/audiobuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/average.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/env.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/file.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/intmap.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/intr.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/log.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/memory.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/net.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/net_intr.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/process.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/process_intr.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/rand.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/strbuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/str.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/term.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/thread.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/tick.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/timeout.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/sys/win/file.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/sys/win/process.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/usb/aoa_hid.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/usb/gamepad_aoa.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/usb/keyboard_aoa.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/usb/mouse_aoa.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/usb/scrcpy_otg.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/usb/screen_otg.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/usb/usb.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": true, "install_filename": ["C:/msys64/mingw64/bin/scrcpy.exe"]}, {"name": "test_adb_parser", "id": "a172ced@@test_adb_parser@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_adb_parser.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_adb_parser.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_adb_parser.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/adb/adb_device.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/adb/adb_parser.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/str.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/strbuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_binary", "id": "a172ced@@test_binary@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_binary.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_binary.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_binary.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_audiobuf", "id": "a172ced@@test_audiobuf@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_audiobuf.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_audiobuf.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_audiobuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/audiobuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/memory.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_cli", "id": "a172ced@@test_cli@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_cli.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_cli.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_cli.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/cli.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/options.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/log.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/net.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/str.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/strbuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/term.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_control_msg_serialize", "id": "a172ced@@test_control_msg_serialize@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_control_msg_serialize.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_control_msg_serialize.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_control_msg_serialize.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/control_msg.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/str.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/strbuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_device_msg_deserialize", "id": "a172ced@@test_device_msg_deserialize@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_device_msg_deserialize.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_device_msg_deserialize.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_device_msg_deserialize.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/device_msg.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_orientation", "id": "a172ced@@test_orientation@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_orientation.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_orientation.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_orientation.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/options.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_strbuf", "id": "a172ced@@test_strbuf@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_strbuf.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_strbuf.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_strbuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/strbuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_str", "id": "a172ced@@test_str@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_str.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_str.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_str.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/str.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/strbuf.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_vecdeque", "id": "a172ced@@test_vecdeque@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_vecdeque.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_vecdeque.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_vecdeque.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/util/memory.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "test_vector", "id": "a172ced@@test_vector@exe", "type": "executable", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_vector.exe"], "build_by_default": true, "target_sources": [{"language": "c", "machine": "host", "compiler": ["cc"], "parameters": ["-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app/test_vector.exe.p", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app", "-IC:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src", "-IC:/msys64/mingw64/include/SDL2", "-IC:/msys64/mingw64/include/libusb-1.0", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-Wall", "-Winvalid-pch", "-Wextra", "-std=c11", "-O0", "-g", "-Wmissing-prototypes", "-Dmain=SDL_main", "-DSDL_MAIN_HANDLED", "-DSC_TEST"], "sources": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/tests/test_vector.c", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/app/src/compat.c"], "generated_sources": [], "unity_sources": []}, {"linker": ["cc"], "parameters": ["-Wl,--allow-shlib-undefined", "-Wl,--start-group", "-Wl,--start-group", "C:/msys64/mingw64/lib/libavformat.dll.a", "C:/msys64/mingw64/lib/libavcodec.dll.a", "C:/msys64/mingw64/lib/libavutil.dll.a", "C:/msys64/mingw64/lib/libswresample.dll.a", "C:/msys64/mingw64/lib/libmingw32.a", "-mwindows", "C:/msys64/mingw64/lib/libSDL2main.a", "C:/msys64/mingw64/lib/libSDL2.dll.a", "C:/msys64/mingw64/lib/libusb-1.0.dll.a", "-lmingw32", "-lws2_32", "-Wl,--subsystem,console", "-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32", "-Wl,--end-group", "-Wl,--end-group"]}], "extra_files": [], "subproject": null, "dependencies": ["libavformat", "libavcodec", "liba<PERSON><PERSON>", "libswresample", "sdl2", "libusb-1.0", "mingw32", "ws2_32"], "depends": [], "win_subsystem": "console", "installed": false}, {"name": "scrcpy-server", "id": "b3eacd3@@scrcpy-server@cus", "type": "custom", "defined_in": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/server/meson.build", "filename": ["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/server/scrcpy-server"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["bash", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/server/./scripts/build-wrapper.sh", "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/server", "@OUTPUT@", "debug"], "parameters": [], "sources": [], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": true, "install_filename": ["C:/msys64/mingw64/share/scrcpy/scrcpy-server"]}]