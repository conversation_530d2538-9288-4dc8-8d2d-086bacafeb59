@echo off
REM Build script cho scrcpy trên Windows với clipboard monitoring

echo Building scrcpy with clipboard monitoring support...
echo.

REM Kiểm tra dependencies
echo Checking dependencies...

REM Kiểm tra meson
where meson >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Meson not found in PATH.
    echo.
    echo Please install Meson first:
    echo 1. Download from: https://mesonbuild.com/Getting-meson.html
    echo 2. Or install via pip: pip install meson
    echo 3. Or install via chocolatey: choco install meson
    echo.
    echo Alternative: Try building with MINGW64/MSYS2
    echo.
    pause
    exit /b 1
)

REM Kiểm tra ninja
where ninja >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Ninja not found in PATH.
    echo.
    echo Please install Ninja first:
    echo 1. Download from: https://ninja-build.org/
    echo 2. Or install via chocolatey: choco install ninja
    echo.
    pause
    exit /b 1
)

REM Kiểm tra compiler
where cl >nul 2>&1
set HAVE_MSVC=%errorlevel%

where gcc >nul 2>&1
set HAVE_GCC=%errorlevel%

if %HAVE_MSVC% neq 0 if %HAVE_GCC% neq 0 (
    echo ERROR: No compiler found.
    echo.
    echo Please install one of:
    echo 1. Visual Studio Build Tools or Visual Studio Community
    echo    - Run this script from "Developer Command Prompt"
    echo 2. MINGW64/MSYS2 with GCC
    echo    - Run this script from MINGW64 terminal
    echo.
    pause
    exit /b 1
)

if %HAVE_MSVC% equ 0 (
    echo Found MSVC compiler
    set BUILD_TYPE=native
) else (
    echo Found GCC compiler
    set BUILD_TYPE=native
)

REM Tạo build directory
if not exist build-win64 (
    echo Creating build directory...
    mkdir build-win64
)

REM Configure build
echo Configuring build...
if %HAVE_MSVC% equ 0 (
    echo Using MSVC compiler...
    meson setup build-win64
) else (
    echo Using GCC compiler...
    meson setup build-win64
)

if %errorlevel% neq 0 (
    echo ERROR: Meson setup failed
    echo.
    echo Try these solutions:
    echo 1. Make sure all dependencies are installed
    echo 2. Run from correct terminal (Developer Command Prompt for MSVC, MINGW64 for GCC)
    echo 3. Check meson-log.txt for detailed error
    echo.
    pause
    exit /b 1
)

REM Build
echo Building...
meson compile -C build-win64
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    echo.
    echo Check build-win64\meson-logs\meson-log.txt for details
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable location: build-win64\app\scrcpy.exe
echo.
echo To test clipboard-only mode:
echo   build-win64\app\scrcpy.exe --clipboard-only
echo.
echo To see all options:
echo   build-win64\app\scrcpy.exe --help
echo.

pause
