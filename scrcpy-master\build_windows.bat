@echo off
REM Build script cho scrcpy trên Windows với clipboard monitoring

echo Building scrcpy with clipboard monitoring support...

REM Kiểm tra dependencies
echo Checking dependencies...

where meson >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Meson not found. Please install Meson first.
    echo Download from: https://mesonbuild.com/Getting-meson.html
    pause
    exit /b 1
)

where ninja >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Ninja not found. Please install Ninja first.
    echo Download from: https://ninja-build.org/
    pause
    exit /b 1
)

where cl >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Visual Studio Build Tools not found.
    echo Please install Visual Studio Build Tools or Visual Studio Community.
    echo Make sure to run this script from "Developer Command Prompt"
    pause
    exit /b 1
)

REM Tạo build directory
if not exist build-win64 (
    echo Creating build directory...
    mkdir build-win64
)

REM Configure build
echo Configuring build...
meson setup build-win64 --cross-file=cross_win64.txt
if %errorlevel% neq 0 (
    echo ERROR: Meson setup failed
    pause
    exit /b 1
)

REM Build
echo Building...
meson compile -C build-win64
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Executable location: build-win64\app\scrcpy.exe
echo.
echo To test clipboard-only mode:
echo   build-win64\app\scrcpy.exe --clipboard-only
echo.
echo To see all options:
echo   build-win64\app\scrcpy.exe --help
echo.

pause
