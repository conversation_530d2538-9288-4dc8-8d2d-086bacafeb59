# Build Scrcpy với Clipboard Monitoring trên MINGW64

## Yêu cầu

### 1. MSYS2/MINGW64
Download và cài đặt MSYS2 từ: https://www.msys2.org/

### 2. Cài đặt dependencies trong MINGW64 terminal:

```bash
# Update package database
pacman -Syu

# Install build tools
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-meson
pacman -S mingw-w64-x86_64-ninja
pacman -S mingw-w64-x86_64-pkg-config

# Install libraries
pacman -S mingw-w64-x86_64-SDL2
pacman -S mingw-w64-x86_64-ffmpeg
pacman -S mingw-w64-x86_64-libusb
```

## Build Steps

### 1. Mở MINGW64 terminal (không phải PowerShell)

### 2. Navigate tới project directory:
```bash
cd /c/Users/<USER>/Pictures/scrcpy/scrcpy-master
```

### 3. Setup build:
```bash
# Native build (không dùng cross-file)
meson setup build-win64-native

# Hoặc nếu muốn dùng cross-file:
meson setup build-win64 --cross-file=cross_win64.txt
```

### 4. Build:
```bash
meson compile -C build-win64-native
```

### 5. Test:
```bash
# Test clipboard-only mode
./build-win64-native/app/scrcpy.exe --clipboard-only

# Test normal mode với clipboard monitoring
./build-win64-native/app/scrcpy.exe --clipboard-monitor
```

## Troubleshooting

### Lỗi "Unknown linker"
- Đảm bảo đang dùng MINGW64 terminal, không phải PowerShell
- Kiểm tra PATH có chứa MINGW64 tools

### Lỗi "meson not found"
```bash
# Cài đặt meson
pacman -S mingw-w64-x86_64-meson
```

### Lỗi missing libraries
```bash
# Cài đặt tất cả dependencies
pacman -S mingw-w64-x86_64-SDL2 mingw-w64-x86_64-ffmpeg mingw-w64-x86_64-libusb
```

### Lỗi "pkg-config not found"
```bash
pacman -S mingw-w64-x86_64-pkg-config
```

## Alternative: Pre-built Dependencies

Nếu gặp khó khăn với build, có thể download pre-built dependencies:

1. Download scrcpy dependencies từ: https://github.com/Genymobile/scrcpy/releases
2. Extract vào thư mục `deps/`
3. Build với dependencies có sẵn

## Kiểm tra Build

Sau khi build thành công:

```bash
# Kiểm tra executable
ls -la build-win64-native/app/scrcpy.exe

# Kiểm tra dependencies
ldd build-win64-native/app/scrcpy.exe

# Test help
./build-win64-native/app/scrcpy.exe --help | grep clipboard
```

Expected output:
```
--clipboard-monitor     Enable automatic clipboard monitoring
--clipboard-only        Run in clipboard-only mode
--no-clipboard-autosync Disable automatic clipboard sync
```

## Environment Variables

Có thể cần set environment variables:

```bash
export PKG_CONFIG_PATH="/mingw64/lib/pkgconfig"
export PATH="/mingw64/bin:$PATH"
```

## Build với Debug Info

Để debug:

```bash
meson setup build-debug --buildtype=debug
meson compile -C build-debug
```

## Clean Build

Để build lại từ đầu:

```bash
rm -rf build-win64-native
meson setup build-win64-native
meson compile -C build-win64-native
```
