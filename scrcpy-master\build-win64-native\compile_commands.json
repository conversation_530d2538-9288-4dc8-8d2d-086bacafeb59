[{"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_main.c.obj -MF \"app/scrcpy.exe.p/src_main.c.obj.d\" -o app/scrcpy.exe.p/src_main.c.obj \"-c\" ../app/src/main.c", "file": "../app/src/main.c", "output": "app/scrcpy.exe.p/src_main.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_adb_adb.c.obj -MF \"app/scrcpy.exe.p/src_adb_adb.c.obj.d\" -o app/scrcpy.exe.p/src_adb_adb.c.obj \"-c\" ../app/src/adb/adb.c", "file": "../app/src/adb/adb.c", "output": "app/scrcpy.exe.p/src_adb_adb.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_adb_adb_device.c.obj -MF \"app/scrcpy.exe.p/src_adb_adb_device.c.obj.d\" -o app/scrcpy.exe.p/src_adb_adb_device.c.obj \"-c\" ../app/src/adb/adb_device.c", "file": "../app/src/adb/adb_device.c", "output": "app/scrcpy.exe.p/src_adb_adb_device.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_adb_adb_parser.c.obj -MF \"app/scrcpy.exe.p/src_adb_adb_parser.c.obj.d\" -o app/scrcpy.exe.p/src_adb_adb_parser.c.obj \"-c\" ../app/src/adb/adb_parser.c", "file": "../app/src/adb/adb_parser.c", "output": "app/scrcpy.exe.p/src_adb_adb_parser.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_adb_adb_tunnel.c.obj -MF \"app/scrcpy.exe.p/src_adb_adb_tunnel.c.obj.d\" -o app/scrcpy.exe.p/src_adb_adb_tunnel.c.obj \"-c\" ../app/src/adb/adb_tunnel.c", "file": "../app/src/adb/adb_tunnel.c", "output": "app/scrcpy.exe.p/src_adb_adb_tunnel.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_audio_player.c.obj -MF \"app/scrcpy.exe.p/src_audio_player.c.obj.d\" -o app/scrcpy.exe.p/src_audio_player.c.obj \"-c\" ../app/src/audio_player.c", "file": "../app/src/audio_player.c", "output": "app/scrcpy.exe.p/src_audio_player.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_audio_regulator.c.obj -MF \"app/scrcpy.exe.p/src_audio_regulator.c.obj.d\" -o app/scrcpy.exe.p/src_audio_regulator.c.obj \"-c\" ../app/src/audio_regulator.c", "file": "../app/src/audio_regulator.c", "output": "app/scrcpy.exe.p/src_audio_regulator.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_cli.c.obj -MF \"app/scrcpy.exe.p/src_cli.c.obj.d\" -o app/scrcpy.exe.p/src_cli.c.obj \"-c\" ../app/src/cli.c", "file": "../app/src/cli.c", "output": "app/scrcpy.exe.p/src_cli.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_clipboard_monitor.c.obj -MF \"app/scrcpy.exe.p/src_clipboard_monitor.c.obj.d\" -o app/scrcpy.exe.p/src_clipboard_monitor.c.obj \"-c\" ../app/src/clipboard_monitor.c", "file": "../app/src/clipboard_monitor.c", "output": "app/scrcpy.exe.p/src_clipboard_monitor.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_clock.c.obj -MF \"app/scrcpy.exe.p/src_clock.c.obj.d\" -o app/scrcpy.exe.p/src_clock.c.obj \"-c\" ../app/src/clock.c", "file": "../app/src/clock.c", "output": "app/scrcpy.exe.p/src_clock.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_compat.c.obj -MF \"app/scrcpy.exe.p/src_compat.c.obj.d\" -o app/scrcpy.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/scrcpy.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_control_msg.c.obj -MF \"app/scrcpy.exe.p/src_control_msg.c.obj.d\" -o app/scrcpy.exe.p/src_control_msg.c.obj \"-c\" ../app/src/control_msg.c", "file": "../app/src/control_msg.c", "output": "app/scrcpy.exe.p/src_control_msg.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_controller.c.obj -MF \"app/scrcpy.exe.p/src_controller.c.obj.d\" -o app/scrcpy.exe.p/src_controller.c.obj \"-c\" ../app/src/controller.c", "file": "../app/src/controller.c", "output": "app/scrcpy.exe.p/src_controller.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_decoder.c.obj -MF \"app/scrcpy.exe.p/src_decoder.c.obj.d\" -o app/scrcpy.exe.p/src_decoder.c.obj \"-c\" ../app/src/decoder.c", "file": "../app/src/decoder.c", "output": "app/scrcpy.exe.p/src_decoder.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_delay_buffer.c.obj -MF \"app/scrcpy.exe.p/src_delay_buffer.c.obj.d\" -o app/scrcpy.exe.p/src_delay_buffer.c.obj \"-c\" ../app/src/delay_buffer.c", "file": "../app/src/delay_buffer.c", "output": "app/scrcpy.exe.p/src_delay_buffer.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_demuxer.c.obj -MF \"app/scrcpy.exe.p/src_demuxer.c.obj.d\" -o app/scrcpy.exe.p/src_demuxer.c.obj \"-c\" ../app/src/demuxer.c", "file": "../app/src/demuxer.c", "output": "app/scrcpy.exe.p/src_demuxer.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_device_msg.c.obj -MF \"app/scrcpy.exe.p/src_device_msg.c.obj.d\" -o app/scrcpy.exe.p/src_device_msg.c.obj \"-c\" ../app/src/device_msg.c", "file": "../app/src/device_msg.c", "output": "app/scrcpy.exe.p/src_device_msg.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_display.c.obj -MF \"app/scrcpy.exe.p/src_display.c.obj.d\" -o app/scrcpy.exe.p/src_display.c.obj \"-c\" ../app/src/display.c", "file": "../app/src/display.c", "output": "app/scrcpy.exe.p/src_display.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_events.c.obj -MF \"app/scrcpy.exe.p/src_events.c.obj.d\" -o app/scrcpy.exe.p/src_events.c.obj \"-c\" ../app/src/events.c", "file": "../app/src/events.c", "output": "app/scrcpy.exe.p/src_events.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_icon.c.obj -MF \"app/scrcpy.exe.p/src_icon.c.obj.d\" -o app/scrcpy.exe.p/src_icon.c.obj \"-c\" ../app/src/icon.c", "file": "../app/src/icon.c", "output": "app/scrcpy.exe.p/src_icon.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_file_pusher.c.obj -MF \"app/scrcpy.exe.p/src_file_pusher.c.obj.d\" -o app/scrcpy.exe.p/src_file_pusher.c.obj \"-c\" ../app/src/file_pusher.c", "file": "../app/src/file_pusher.c", "output": "app/scrcpy.exe.p/src_file_pusher.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_fps_counter.c.obj -MF \"app/scrcpy.exe.p/src_fps_counter.c.obj.d\" -o app/scrcpy.exe.p/src_fps_counter.c.obj \"-c\" ../app/src/fps_counter.c", "file": "../app/src/fps_counter.c", "output": "app/scrcpy.exe.p/src_fps_counter.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_frame_buffer.c.obj -MF \"app/scrcpy.exe.p/src_frame_buffer.c.obj.d\" -o app/scrcpy.exe.p/src_frame_buffer.c.obj \"-c\" ../app/src/frame_buffer.c", "file": "../app/src/frame_buffer.c", "output": "app/scrcpy.exe.p/src_frame_buffer.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_input_manager.c.obj -MF \"app/scrcpy.exe.p/src_input_manager.c.obj.d\" -o app/scrcpy.exe.p/src_input_manager.c.obj \"-c\" ../app/src/input_manager.c", "file": "../app/src/input_manager.c", "output": "app/scrcpy.exe.p/src_input_manager.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_keyboard_sdk.c.obj -MF \"app/scrcpy.exe.p/src_keyboard_sdk.c.obj.d\" -o app/scrcpy.exe.p/src_keyboard_sdk.c.obj \"-c\" ../app/src/keyboard_sdk.c", "file": "../app/src/keyboard_sdk.c", "output": "app/scrcpy.exe.p/src_keyboard_sdk.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_mouse_capture.c.obj -MF \"app/scrcpy.exe.p/src_mouse_capture.c.obj.d\" -o app/scrcpy.exe.p/src_mouse_capture.c.obj \"-c\" ../app/src/mouse_capture.c", "file": "../app/src/mouse_capture.c", "output": "app/scrcpy.exe.p/src_mouse_capture.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_mouse_sdk.c.obj -MF \"app/scrcpy.exe.p/src_mouse_sdk.c.obj.d\" -o app/scrcpy.exe.p/src_mouse_sdk.c.obj \"-c\" ../app/src/mouse_sdk.c", "file": "../app/src/mouse_sdk.c", "output": "app/scrcpy.exe.p/src_mouse_sdk.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_opengl.c.obj -MF \"app/scrcpy.exe.p/src_opengl.c.obj.d\" -o app/scrcpy.exe.p/src_opengl.c.obj \"-c\" ../app/src/opengl.c", "file": "../app/src/opengl.c", "output": "app/scrcpy.exe.p/src_opengl.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_options.c.obj -MF \"app/scrcpy.exe.p/src_options.c.obj.d\" -o app/scrcpy.exe.p/src_options.c.obj \"-c\" ../app/src/options.c", "file": "../app/src/options.c", "output": "app/scrcpy.exe.p/src_options.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_packet_merger.c.obj -MF \"app/scrcpy.exe.p/src_packet_merger.c.obj.d\" -o app/scrcpy.exe.p/src_packet_merger.c.obj \"-c\" ../app/src/packet_merger.c", "file": "../app/src/packet_merger.c", "output": "app/scrcpy.exe.p/src_packet_merger.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_receiver.c.obj -MF \"app/scrcpy.exe.p/src_receiver.c.obj.d\" -o app/scrcpy.exe.p/src_receiver.c.obj \"-c\" ../app/src/receiver.c", "file": "../app/src/receiver.c", "output": "app/scrcpy.exe.p/src_receiver.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_recorder.c.obj -MF \"app/scrcpy.exe.p/src_recorder.c.obj.d\" -o app/scrcpy.exe.p/src_recorder.c.obj \"-c\" ../app/src/recorder.c", "file": "../app/src/recorder.c", "output": "app/scrcpy.exe.p/src_recorder.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_scrcpy.c.obj -MF \"app/scrcpy.exe.p/src_scrcpy.c.obj.d\" -o app/scrcpy.exe.p/src_scrcpy.c.obj \"-c\" ../app/src/scrcpy.c", "file": "../app/src/scrcpy.c", "output": "app/scrcpy.exe.p/src_scrcpy.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_screen.c.obj -MF \"app/scrcpy.exe.p/src_screen.c.obj.d\" -o app/scrcpy.exe.p/src_screen.c.obj \"-c\" ../app/src/screen.c", "file": "../app/src/screen.c", "output": "app/scrcpy.exe.p/src_screen.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_server.c.obj -MF \"app/scrcpy.exe.p/src_server.c.obj.d\" -o app/scrcpy.exe.p/src_server.c.obj \"-c\" ../app/src/server.c", "file": "../app/src/server.c", "output": "app/scrcpy.exe.p/src_server.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_version.c.obj -MF \"app/scrcpy.exe.p/src_version.c.obj.d\" -o app/scrcpy.exe.p/src_version.c.obj \"-c\" ../app/src/version.c", "file": "../app/src/version.c", "output": "app/scrcpy.exe.p/src_version.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_hid_hid_gamepad.c.obj -MF \"app/scrcpy.exe.p/src_hid_hid_gamepad.c.obj.d\" -o app/scrcpy.exe.p/src_hid_hid_gamepad.c.obj \"-c\" ../app/src/hid/hid_gamepad.c", "file": "../app/src/hid/hid_gamepad.c", "output": "app/scrcpy.exe.p/src_hid_hid_gamepad.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_hid_hid_keyboard.c.obj -MF \"app/scrcpy.exe.p/src_hid_hid_keyboard.c.obj.d\" -o app/scrcpy.exe.p/src_hid_hid_keyboard.c.obj \"-c\" ../app/src/hid/hid_keyboard.c", "file": "../app/src/hid/hid_keyboard.c", "output": "app/scrcpy.exe.p/src_hid_hid_keyboard.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_hid_hid_mouse.c.obj -MF \"app/scrcpy.exe.p/src_hid_hid_mouse.c.obj.d\" -o app/scrcpy.exe.p/src_hid_hid_mouse.c.obj \"-c\" ../app/src/hid/hid_mouse.c", "file": "../app/src/hid/hid_mouse.c", "output": "app/scrcpy.exe.p/src_hid_hid_mouse.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_trait_frame_source.c.obj -MF \"app/scrcpy.exe.p/src_trait_frame_source.c.obj.d\" -o app/scrcpy.exe.p/src_trait_frame_source.c.obj \"-c\" ../app/src/trait/frame_source.c", "file": "../app/src/trait/frame_source.c", "output": "app/scrcpy.exe.p/src_trait_frame_source.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_trait_packet_source.c.obj -MF \"app/scrcpy.exe.p/src_trait_packet_source.c.obj.d\" -o app/scrcpy.exe.p/src_trait_packet_source.c.obj \"-c\" ../app/src/trait/packet_source.c", "file": "../app/src/trait/packet_source.c", "output": "app/scrcpy.exe.p/src_trait_packet_source.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_uhid_gamepad_uhid.c.obj -MF \"app/scrcpy.exe.p/src_uhid_gamepad_uhid.c.obj.d\" -o app/scrcpy.exe.p/src_uhid_gamepad_uhid.c.obj \"-c\" ../app/src/uhid/gamepad_uhid.c", "file": "../app/src/uhid/gamepad_uhid.c", "output": "app/scrcpy.exe.p/src_uhid_gamepad_uhid.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_uhid_keyboard_uhid.c.obj -MF \"app/scrcpy.exe.p/src_uhid_keyboard_uhid.c.obj.d\" -o app/scrcpy.exe.p/src_uhid_keyboard_uhid.c.obj \"-c\" ../app/src/uhid/keyboard_uhid.c", "file": "../app/src/uhid/keyboard_uhid.c", "output": "app/scrcpy.exe.p/src_uhid_keyboard_uhid.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_uhid_mouse_uhid.c.obj -MF \"app/scrcpy.exe.p/src_uhid_mouse_uhid.c.obj.d\" -o app/scrcpy.exe.p/src_uhid_mouse_uhid.c.obj \"-c\" ../app/src/uhid/mouse_uhid.c", "file": "../app/src/uhid/mouse_uhid.c", "output": "app/scrcpy.exe.p/src_uhid_mouse_uhid.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_uhid_uhid_output.c.obj -MF \"app/scrcpy.exe.p/src_uhid_uhid_output.c.obj.d\" -o app/scrcpy.exe.p/src_uhid_uhid_output.c.obj \"-c\" ../app/src/uhid/uhid_output.c", "file": "../app/src/uhid/uhid_output.c", "output": "app/scrcpy.exe.p/src_uhid_uhid_output.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_acksync.c.obj -MF \"app/scrcpy.exe.p/src_util_acksync.c.obj.d\" -o app/scrcpy.exe.p/src_util_acksync.c.obj \"-c\" ../app/src/util/acksync.c", "file": "../app/src/util/acksync.c", "output": "app/scrcpy.exe.p/src_util_acksync.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_audiobuf.c.obj -MF \"app/scrcpy.exe.p/src_util_audiobuf.c.obj.d\" -o app/scrcpy.exe.p/src_util_audiobuf.c.obj \"-c\" ../app/src/util/audiobuf.c", "file": "../app/src/util/audiobuf.c", "output": "app/scrcpy.exe.p/src_util_audiobuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_average.c.obj -MF \"app/scrcpy.exe.p/src_util_average.c.obj.d\" -o app/scrcpy.exe.p/src_util_average.c.obj \"-c\" ../app/src/util/average.c", "file": "../app/src/util/average.c", "output": "app/scrcpy.exe.p/src_util_average.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_env.c.obj -MF \"app/scrcpy.exe.p/src_util_env.c.obj.d\" -o app/scrcpy.exe.p/src_util_env.c.obj \"-c\" ../app/src/util/env.c", "file": "../app/src/util/env.c", "output": "app/scrcpy.exe.p/src_util_env.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_file.c.obj -MF \"app/scrcpy.exe.p/src_util_file.c.obj.d\" -o app/scrcpy.exe.p/src_util_file.c.obj \"-c\" ../app/src/util/file.c", "file": "../app/src/util/file.c", "output": "app/scrcpy.exe.p/src_util_file.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_intmap.c.obj -MF \"app/scrcpy.exe.p/src_util_intmap.c.obj.d\" -o app/scrcpy.exe.p/src_util_intmap.c.obj \"-c\" ../app/src/util/intmap.c", "file": "../app/src/util/intmap.c", "output": "app/scrcpy.exe.p/src_util_intmap.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_intr.c.obj -MF \"app/scrcpy.exe.p/src_util_intr.c.obj.d\" -o app/scrcpy.exe.p/src_util_intr.c.obj \"-c\" ../app/src/util/intr.c", "file": "../app/src/util/intr.c", "output": "app/scrcpy.exe.p/src_util_intr.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_log.c.obj -MF \"app/scrcpy.exe.p/src_util_log.c.obj.d\" -o app/scrcpy.exe.p/src_util_log.c.obj \"-c\" ../app/src/util/log.c", "file": "../app/src/util/log.c", "output": "app/scrcpy.exe.p/src_util_log.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_memory.c.obj -MF \"app/scrcpy.exe.p/src_util_memory.c.obj.d\" -o app/scrcpy.exe.p/src_util_memory.c.obj \"-c\" ../app/src/util/memory.c", "file": "../app/src/util/memory.c", "output": "app/scrcpy.exe.p/src_util_memory.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_net.c.obj -MF \"app/scrcpy.exe.p/src_util_net.c.obj.d\" -o app/scrcpy.exe.p/src_util_net.c.obj \"-c\" ../app/src/util/net.c", "file": "../app/src/util/net.c", "output": "app/scrcpy.exe.p/src_util_net.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_net_intr.c.obj -MF \"app/scrcpy.exe.p/src_util_net_intr.c.obj.d\" -o app/scrcpy.exe.p/src_util_net_intr.c.obj \"-c\" ../app/src/util/net_intr.c", "file": "../app/src/util/net_intr.c", "output": "app/scrcpy.exe.p/src_util_net_intr.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_process.c.obj -MF \"app/scrcpy.exe.p/src_util_process.c.obj.d\" -o app/scrcpy.exe.p/src_util_process.c.obj \"-c\" ../app/src/util/process.c", "file": "../app/src/util/process.c", "output": "app/scrcpy.exe.p/src_util_process.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_process_intr.c.obj -MF \"app/scrcpy.exe.p/src_util_process_intr.c.obj.d\" -o app/scrcpy.exe.p/src_util_process_intr.c.obj \"-c\" ../app/src/util/process_intr.c", "file": "../app/src/util/process_intr.c", "output": "app/scrcpy.exe.p/src_util_process_intr.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_rand.c.obj -MF \"app/scrcpy.exe.p/src_util_rand.c.obj.d\" -o app/scrcpy.exe.p/src_util_rand.c.obj \"-c\" ../app/src/util/rand.c", "file": "../app/src/util/rand.c", "output": "app/scrcpy.exe.p/src_util_rand.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_strbuf.c.obj -MF \"app/scrcpy.exe.p/src_util_strbuf.c.obj.d\" -o app/scrcpy.exe.p/src_util_strbuf.c.obj \"-c\" ../app/src/util/strbuf.c", "file": "../app/src/util/strbuf.c", "output": "app/scrcpy.exe.p/src_util_strbuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_str.c.obj -MF \"app/scrcpy.exe.p/src_util_str.c.obj.d\" -o app/scrcpy.exe.p/src_util_str.c.obj \"-c\" ../app/src/util/str.c", "file": "../app/src/util/str.c", "output": "app/scrcpy.exe.p/src_util_str.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_term.c.obj -MF \"app/scrcpy.exe.p/src_util_term.c.obj.d\" -o app/scrcpy.exe.p/src_util_term.c.obj \"-c\" ../app/src/util/term.c", "file": "../app/src/util/term.c", "output": "app/scrcpy.exe.p/src_util_term.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_thread.c.obj -MF \"app/scrcpy.exe.p/src_util_thread.c.obj.d\" -o app/scrcpy.exe.p/src_util_thread.c.obj \"-c\" ../app/src/util/thread.c", "file": "../app/src/util/thread.c", "output": "app/scrcpy.exe.p/src_util_thread.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_tick.c.obj -MF \"app/scrcpy.exe.p/src_util_tick.c.obj.d\" -o app/scrcpy.exe.p/src_util_tick.c.obj \"-c\" ../app/src/util/tick.c", "file": "../app/src/util/tick.c", "output": "app/scrcpy.exe.p/src_util_tick.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_util_timeout.c.obj -MF \"app/scrcpy.exe.p/src_util_timeout.c.obj.d\" -o app/scrcpy.exe.p/src_util_timeout.c.obj \"-c\" ../app/src/util/timeout.c", "file": "../app/src/util/timeout.c", "output": "app/scrcpy.exe.p/src_util_timeout.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_sys_win_file.c.obj -MF \"app/scrcpy.exe.p/src_sys_win_file.c.obj.d\" -o app/scrcpy.exe.p/src_sys_win_file.c.obj \"-c\" ../app/src/sys/win/file.c", "file": "../app/src/sys/win/file.c", "output": "app/scrcpy.exe.p/src_sys_win_file.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_sys_win_process.c.obj -MF \"app/scrcpy.exe.p/src_sys_win_process.c.obj.d\" -o app/scrcpy.exe.p/src_sys_win_process.c.obj \"-c\" ../app/src/sys/win/process.c", "file": "../app/src/sys/win/process.c", "output": "app/scrcpy.exe.p/src_sys_win_process.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_usb_aoa_hid.c.obj -MF \"app/scrcpy.exe.p/src_usb_aoa_hid.c.obj.d\" -o app/scrcpy.exe.p/src_usb_aoa_hid.c.obj \"-c\" ../app/src/usb/aoa_hid.c", "file": "../app/src/usb/aoa_hid.c", "output": "app/scrcpy.exe.p/src_usb_aoa_hid.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_usb_gamepad_aoa.c.obj -MF \"app/scrcpy.exe.p/src_usb_gamepad_aoa.c.obj.d\" -o app/scrcpy.exe.p/src_usb_gamepad_aoa.c.obj \"-c\" ../app/src/usb/gamepad_aoa.c", "file": "../app/src/usb/gamepad_aoa.c", "output": "app/scrcpy.exe.p/src_usb_gamepad_aoa.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_usb_keyboard_aoa.c.obj -MF \"app/scrcpy.exe.p/src_usb_keyboard_aoa.c.obj.d\" -o app/scrcpy.exe.p/src_usb_keyboard_aoa.c.obj \"-c\" ../app/src/usb/keyboard_aoa.c", "file": "../app/src/usb/keyboard_aoa.c", "output": "app/scrcpy.exe.p/src_usb_keyboard_aoa.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_usb_mouse_aoa.c.obj -MF \"app/scrcpy.exe.p/src_usb_mouse_aoa.c.obj.d\" -o app/scrcpy.exe.p/src_usb_mouse_aoa.c.obj \"-c\" ../app/src/usb/mouse_aoa.c", "file": "../app/src/usb/mouse_aoa.c", "output": "app/scrcpy.exe.p/src_usb_mouse_aoa.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_usb_scrcpy_otg.c.obj -MF \"app/scrcpy.exe.p/src_usb_scrcpy_otg.c.obj.d\" -o app/scrcpy.exe.p/src_usb_scrcpy_otg.c.obj \"-c\" ../app/src/usb/scrcpy_otg.c", "file": "../app/src/usb/scrcpy_otg.c", "output": "app/scrcpy.exe.p/src_usb_scrcpy_otg.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_usb_screen_otg.c.obj -MF \"app/scrcpy.exe.p/src_usb_screen_otg.c.obj.d\" -o app/scrcpy.exe.p/src_usb_screen_otg.c.obj \"-c\" ../app/src/usb/screen_otg.c", "file": "../app/src/usb/screen_otg.c", "output": "app/scrcpy.exe.p/src_usb_screen_otg.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/scrcpy.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" -MD -MQ app/scrcpy.exe.p/src_usb_usb.c.obj -MF \"app/scrcpy.exe.p/src_usb_usb.c.obj.d\" -o app/scrcpy.exe.p/src_usb_usb.c.obj \"-c\" ../app/src/usb/usb.c", "file": "../app/src/usb/usb.c", "output": "app/scrcpy.exe.p/src_usb_usb.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_adb_parser.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_adb_parser.exe.p/tests_test_adb_parser.c.obj -MF \"app/test_adb_parser.exe.p/tests_test_adb_parser.c.obj.d\" -o app/test_adb_parser.exe.p/tests_test_adb_parser.c.obj \"-c\" ../app/tests/test_adb_parser.c", "file": "../app/tests/test_adb_parser.c", "output": "app/test_adb_parser.exe.p/tests_test_adb_parser.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_adb_parser.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_adb_parser.exe.p/src_adb_adb_device.c.obj -MF \"app/test_adb_parser.exe.p/src_adb_adb_device.c.obj.d\" -o app/test_adb_parser.exe.p/src_adb_adb_device.c.obj \"-c\" ../app/src/adb/adb_device.c", "file": "../app/src/adb/adb_device.c", "output": "app/test_adb_parser.exe.p/src_adb_adb_device.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_adb_parser.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_adb_parser.exe.p/src_adb_adb_parser.c.obj -MF \"app/test_adb_parser.exe.p/src_adb_adb_parser.c.obj.d\" -o app/test_adb_parser.exe.p/src_adb_adb_parser.c.obj \"-c\" ../app/src/adb/adb_parser.c", "file": "../app/src/adb/adb_parser.c", "output": "app/test_adb_parser.exe.p/src_adb_adb_parser.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_adb_parser.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_adb_parser.exe.p/src_util_str.c.obj -MF \"app/test_adb_parser.exe.p/src_util_str.c.obj.d\" -o app/test_adb_parser.exe.p/src_util_str.c.obj \"-c\" ../app/src/util/str.c", "file": "../app/src/util/str.c", "output": "app/test_adb_parser.exe.p/src_util_str.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_adb_parser.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_adb_parser.exe.p/src_util_strbuf.c.obj -MF \"app/test_adb_parser.exe.p/src_util_strbuf.c.obj.d\" -o app/test_adb_parser.exe.p/src_util_strbuf.c.obj \"-c\" ../app/src/util/strbuf.c", "file": "../app/src/util/strbuf.c", "output": "app/test_adb_parser.exe.p/src_util_strbuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_adb_parser.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_adb_parser.exe.p/src_compat.c.obj -MF \"app/test_adb_parser.exe.p/src_compat.c.obj.d\" -o app/test_adb_parser.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_adb_parser.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_binary.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_binary.exe.p/tests_test_binary.c.obj -MF \"app/test_binary.exe.p/tests_test_binary.c.obj.d\" -o app/test_binary.exe.p/tests_test_binary.c.obj \"-c\" ../app/tests/test_binary.c", "file": "../app/tests/test_binary.c", "output": "app/test_binary.exe.p/tests_test_binary.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_binary.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_binary.exe.p/src_compat.c.obj -MF \"app/test_binary.exe.p/src_compat.c.obj.d\" -o app/test_binary.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_binary.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_audiobuf.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_audiobuf.exe.p/tests_test_audiobuf.c.obj -MF \"app/test_audiobuf.exe.p/tests_test_audiobuf.c.obj.d\" -o app/test_audiobuf.exe.p/tests_test_audiobuf.c.obj \"-c\" ../app/tests/test_audiobuf.c", "file": "../app/tests/test_audiobuf.c", "output": "app/test_audiobuf.exe.p/tests_test_audiobuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_audiobuf.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_audiobuf.exe.p/src_util_audiobuf.c.obj -MF \"app/test_audiobuf.exe.p/src_util_audiobuf.c.obj.d\" -o app/test_audiobuf.exe.p/src_util_audiobuf.c.obj \"-c\" ../app/src/util/audiobuf.c", "file": "../app/src/util/audiobuf.c", "output": "app/test_audiobuf.exe.p/src_util_audiobuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_audiobuf.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_audiobuf.exe.p/src_util_memory.c.obj -MF \"app/test_audiobuf.exe.p/src_util_memory.c.obj.d\" -o app/test_audiobuf.exe.p/src_util_memory.c.obj \"-c\" ../app/src/util/memory.c", "file": "../app/src/util/memory.c", "output": "app/test_audiobuf.exe.p/src_util_memory.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_audiobuf.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_audiobuf.exe.p/src_compat.c.obj -MF \"app/test_audiobuf.exe.p/src_compat.c.obj.d\" -o app/test_audiobuf.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_audiobuf.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/tests_test_cli.c.obj -MF \"app/test_cli.exe.p/tests_test_cli.c.obj.d\" -o app/test_cli.exe.p/tests_test_cli.c.obj \"-c\" ../app/tests/test_cli.c", "file": "../app/tests/test_cli.c", "output": "app/test_cli.exe.p/tests_test_cli.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/src_cli.c.obj -MF \"app/test_cli.exe.p/src_cli.c.obj.d\" -o app/test_cli.exe.p/src_cli.c.obj \"-c\" ../app/src/cli.c", "file": "../app/src/cli.c", "output": "app/test_cli.exe.p/src_cli.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/src_options.c.obj -MF \"app/test_cli.exe.p/src_options.c.obj.d\" -o app/test_cli.exe.p/src_options.c.obj \"-c\" ../app/src/options.c", "file": "../app/src/options.c", "output": "app/test_cli.exe.p/src_options.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/src_util_log.c.obj -MF \"app/test_cli.exe.p/src_util_log.c.obj.d\" -o app/test_cli.exe.p/src_util_log.c.obj \"-c\" ../app/src/util/log.c", "file": "../app/src/util/log.c", "output": "app/test_cli.exe.p/src_util_log.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/src_util_net.c.obj -MF \"app/test_cli.exe.p/src_util_net.c.obj.d\" -o app/test_cli.exe.p/src_util_net.c.obj \"-c\" ../app/src/util/net.c", "file": "../app/src/util/net.c", "output": "app/test_cli.exe.p/src_util_net.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/src_util_str.c.obj -MF \"app/test_cli.exe.p/src_util_str.c.obj.d\" -o app/test_cli.exe.p/src_util_str.c.obj \"-c\" ../app/src/util/str.c", "file": "../app/src/util/str.c", "output": "app/test_cli.exe.p/src_util_str.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/src_util_strbuf.c.obj -MF \"app/test_cli.exe.p/src_util_strbuf.c.obj.d\" -o app/test_cli.exe.p/src_util_strbuf.c.obj \"-c\" ../app/src/util/strbuf.c", "file": "../app/src/util/strbuf.c", "output": "app/test_cli.exe.p/src_util_strbuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/src_util_term.c.obj -MF \"app/test_cli.exe.p/src_util_term.c.obj.d\" -o app/test_cli.exe.p/src_util_term.c.obj \"-c\" ../app/src/util/term.c", "file": "../app/src/util/term.c", "output": "app/test_cli.exe.p/src_util_term.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_cli.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_cli.exe.p/src_compat.c.obj -MF \"app/test_cli.exe.p/src_compat.c.obj.d\" -o app/test_cli.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_cli.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_control_msg_serialize.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_control_msg_serialize.exe.p/tests_test_control_msg_serialize.c.obj -MF \"app/test_control_msg_serialize.exe.p/tests_test_control_msg_serialize.c.obj.d\" -o app/test_control_msg_serialize.exe.p/tests_test_control_msg_serialize.c.obj \"-c\" ../app/tests/test_control_msg_serialize.c", "file": "../app/tests/test_control_msg_serialize.c", "output": "app/test_control_msg_serialize.exe.p/tests_test_control_msg_serialize.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_control_msg_serialize.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_control_msg_serialize.exe.p/src_control_msg.c.obj -MF \"app/test_control_msg_serialize.exe.p/src_control_msg.c.obj.d\" -o app/test_control_msg_serialize.exe.p/src_control_msg.c.obj \"-c\" ../app/src/control_msg.c", "file": "../app/src/control_msg.c", "output": "app/test_control_msg_serialize.exe.p/src_control_msg.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_control_msg_serialize.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_control_msg_serialize.exe.p/src_util_str.c.obj -MF \"app/test_control_msg_serialize.exe.p/src_util_str.c.obj.d\" -o app/test_control_msg_serialize.exe.p/src_util_str.c.obj \"-c\" ../app/src/util/str.c", "file": "../app/src/util/str.c", "output": "app/test_control_msg_serialize.exe.p/src_util_str.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_control_msg_serialize.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_control_msg_serialize.exe.p/src_util_strbuf.c.obj -MF \"app/test_control_msg_serialize.exe.p/src_util_strbuf.c.obj.d\" -o app/test_control_msg_serialize.exe.p/src_util_strbuf.c.obj \"-c\" ../app/src/util/strbuf.c", "file": "../app/src/util/strbuf.c", "output": "app/test_control_msg_serialize.exe.p/src_util_strbuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_control_msg_serialize.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_control_msg_serialize.exe.p/src_compat.c.obj -MF \"app/test_control_msg_serialize.exe.p/src_compat.c.obj.d\" -o app/test_control_msg_serialize.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_control_msg_serialize.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_device_msg_deserialize.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_device_msg_deserialize.exe.p/tests_test_device_msg_deserialize.c.obj -MF \"app/test_device_msg_deserialize.exe.p/tests_test_device_msg_deserialize.c.obj.d\" -o app/test_device_msg_deserialize.exe.p/tests_test_device_msg_deserialize.c.obj \"-c\" ../app/tests/test_device_msg_deserialize.c", "file": "../app/tests/test_device_msg_deserialize.c", "output": "app/test_device_msg_deserialize.exe.p/tests_test_device_msg_deserialize.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_device_msg_deserialize.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_device_msg_deserialize.exe.p/src_device_msg.c.obj -MF \"app/test_device_msg_deserialize.exe.p/src_device_msg.c.obj.d\" -o app/test_device_msg_deserialize.exe.p/src_device_msg.c.obj \"-c\" ../app/src/device_msg.c", "file": "../app/src/device_msg.c", "output": "app/test_device_msg_deserialize.exe.p/src_device_msg.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_device_msg_deserialize.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_device_msg_deserialize.exe.p/src_compat.c.obj -MF \"app/test_device_msg_deserialize.exe.p/src_compat.c.obj.d\" -o app/test_device_msg_deserialize.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_device_msg_deserialize.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_orientation.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_orientation.exe.p/tests_test_orientation.c.obj -MF \"app/test_orientation.exe.p/tests_test_orientation.c.obj.d\" -o app/test_orientation.exe.p/tests_test_orientation.c.obj \"-c\" ../app/tests/test_orientation.c", "file": "../app/tests/test_orientation.c", "output": "app/test_orientation.exe.p/tests_test_orientation.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_orientation.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_orientation.exe.p/src_options.c.obj -MF \"app/test_orientation.exe.p/src_options.c.obj.d\" -o app/test_orientation.exe.p/src_options.c.obj \"-c\" ../app/src/options.c", "file": "../app/src/options.c", "output": "app/test_orientation.exe.p/src_options.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_orientation.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_orientation.exe.p/src_compat.c.obj -MF \"app/test_orientation.exe.p/src_compat.c.obj.d\" -o app/test_orientation.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_orientation.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_strbuf.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_strbuf.exe.p/tests_test_strbuf.c.obj -MF \"app/test_strbuf.exe.p/tests_test_strbuf.c.obj.d\" -o app/test_strbuf.exe.p/tests_test_strbuf.c.obj \"-c\" ../app/tests/test_strbuf.c", "file": "../app/tests/test_strbuf.c", "output": "app/test_strbuf.exe.p/tests_test_strbuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_strbuf.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_strbuf.exe.p/src_util_strbuf.c.obj -MF \"app/test_strbuf.exe.p/src_util_strbuf.c.obj.d\" -o app/test_strbuf.exe.p/src_util_strbuf.c.obj \"-c\" ../app/src/util/strbuf.c", "file": "../app/src/util/strbuf.c", "output": "app/test_strbuf.exe.p/src_util_strbuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_strbuf.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_strbuf.exe.p/src_compat.c.obj -MF \"app/test_strbuf.exe.p/src_compat.c.obj.d\" -o app/test_strbuf.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_strbuf.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_str.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_str.exe.p/tests_test_str.c.obj -MF \"app/test_str.exe.p/tests_test_str.c.obj.d\" -o app/test_str.exe.p/tests_test_str.c.obj \"-c\" ../app/tests/test_str.c", "file": "../app/tests/test_str.c", "output": "app/test_str.exe.p/tests_test_str.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_str.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_str.exe.p/src_util_str.c.obj -MF \"app/test_str.exe.p/src_util_str.c.obj.d\" -o app/test_str.exe.p/src_util_str.c.obj \"-c\" ../app/src/util/str.c", "file": "../app/src/util/str.c", "output": "app/test_str.exe.p/src_util_str.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_str.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_str.exe.p/src_util_strbuf.c.obj -MF \"app/test_str.exe.p/src_util_strbuf.c.obj.d\" -o app/test_str.exe.p/src_util_strbuf.c.obj \"-c\" ../app/src/util/strbuf.c", "file": "../app/src/util/strbuf.c", "output": "app/test_str.exe.p/src_util_strbuf.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_str.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_str.exe.p/src_compat.c.obj -MF \"app/test_str.exe.p/src_compat.c.obj.d\" -o app/test_str.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_str.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_vecdeque.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_vecdeque.exe.p/tests_test_vecdeque.c.obj -MF \"app/test_vecdeque.exe.p/tests_test_vecdeque.c.obj.d\" -o app/test_vecdeque.exe.p/tests_test_vecdeque.c.obj \"-c\" ../app/tests/test_vecdeque.c", "file": "../app/tests/test_vecdeque.c", "output": "app/test_vecdeque.exe.p/tests_test_vecdeque.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_vecdeque.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_vecdeque.exe.p/src_util_memory.c.obj -MF \"app/test_vecdeque.exe.p/src_util_memory.c.obj.d\" -o app/test_vecdeque.exe.p/src_util_memory.c.obj \"-c\" ../app/src/util/memory.c", "file": "../app/src/util/memory.c", "output": "app/test_vecdeque.exe.p/src_util_memory.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_vecdeque.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_vecdeque.exe.p/src_compat.c.obj -MF \"app/test_vecdeque.exe.p/src_compat.c.obj.d\" -o app/test_vecdeque.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_vecdeque.exe.p/src_compat.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_vector.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_vector.exe.p/tests_test_vector.c.obj -MF \"app/test_vector.exe.p/tests_test_vector.c.obj.d\" -o app/test_vector.exe.p/tests_test_vector.c.obj \"-c\" ../app/tests/test_vector.c", "file": "../app/tests/test_vector.c", "output": "app/test_vector.exe.p/tests_test_vector.c.obj"}, {"directory": "C:\\Users\\<USER>\\Pictures\\scrcpy\\scrcpy-master\\build-win64-native", "command": "\"cc\" \"-Iapp/test_vector.exe.p\" \"-Iapp\" \"-I../app\" \"-I../app/src\" \"-IC:/msys64/mingw64/include/SDL2\" \"-IC:/msys64/mingw64/include/libusb-1.0\" \"-fdiagnostics-color=always\" \"-D_FILE_OFFSET_BITS=64\" \"-Wall\" \"-Winvalid-pch\" \"-Wextra\" \"-std=c11\" \"-O0\" \"-g\" \"-Wmissing-prototypes\" \"-Dmain=SDL_main\" \"-DSDL_MAIN_HANDLED\" \"-DSC_TEST\" -MD -MQ app/test_vector.exe.p/src_compat.c.obj -MF \"app/test_vector.exe.p/src_compat.c.obj.d\" -o app/test_vector.exe.p/src_compat.c.obj \"-c\" ../app/src/compat.c", "file": "../app/src/compat.c", "output": "app/test_vector.exe.p/src_compat.c.obj"}]