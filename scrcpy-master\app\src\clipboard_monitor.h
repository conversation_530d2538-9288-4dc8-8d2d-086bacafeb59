#ifndef SC_CLIPBOARD_MONITOR_H
#define SC_CLIPBOARD_MONITOR_H

#include "common.h"

#include <stdbool.h>

// Forward declarations
struct sc_controller;

/**
 * Clipboard monitor để theo dõi thay đổi clipboard trên Windows
 * và tự động đồng bộ với Android device
 */
struct sc_clipboard_monitor {
    struct sc_controller *controller;
    bool enabled;
    bool monitoring;
    
#ifdef _WIN32
    // Windows-specific data
    void *window_handle;  // HWND
    void *thread_handle;  // HANDLE
    unsigned long thread_id;   // DWORD
#endif
};

/**
 * Khởi tạo clipboard monitor
 * 
 * @param monitor clipboard monitor instance
 * @param controller controller để gửi clipboard data tới device
 * @return true nếu thành công
 */
bool
sc_clipboard_monitor_init(struct sc_clipboard_monitor *monitor,
                         struct sc_controller *controller);

/**
 * Bắt đầu monitoring clipboard changes
 * 
 * @param monitor clipboard monitor instance
 * @return true nếu thành công
 */
bool
sc_clipboard_monitor_start(struct sc_clipboard_monitor *monitor);

/**
 * Dừng monitoring clipboard changes
 * 
 * @param monitor clipboard monitor instance
 */
void
sc_clipboard_monitor_stop(struct sc_clipboard_monitor *monitor);

/**
 * Cleanup clipboard monitor resources
 * 
 * @param monitor clipboard monitor instance
 */
void
sc_clipboard_monitor_destroy(struct sc_clipboard_monitor *monitor);

/**
 * Kiểm tra xem clipboard monitoring có được hỗ trợ không
 * 
 * @return true nếu platform hỗ trợ clipboard monitoring
 */
bool
sc_clipboard_monitor_is_supported(void);

#endif
