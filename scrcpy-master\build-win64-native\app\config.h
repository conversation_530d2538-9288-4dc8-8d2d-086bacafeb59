/*
 * Autogenerated by the Meson build system.
 * Do not edit, your changes will be lost.
 */

#pragma once

#define DEFAULT_LOCAL_PORT_RANGE_FIRST 27183

#define DEFAULT_LOCAL_PORT_RANGE_LAST 27199

#define HAVE_ASPRINTF

#undef HAVE_SOCK_CLOEXEC

#define HAVE_STRDUP

#define HAVE_USB

#undef HAVE_V4L2

#define HAVE_VASPRINTF

#undef PORTABLE

#define PREFIX "C:/msys64/mingw64"

#define SCRCPY_VERSION "3.3.1"

#undef SERVER_DEBUGGER

#define WINVER 0x0600

#define _GNU_SOURCE

#define _POSIX_C_SOURCE 200809L

#define _WIN32_WINNT 0x0600

#define _XOPEN_SOURCE 700

