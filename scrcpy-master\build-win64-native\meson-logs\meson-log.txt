Build started at 2025-07-08T14:37:20.700264
Main binary: C:/msys64/mingw64/bin/python.exe
Build Options: 
Python system: Windows
The Meson build system
Version: 1.8.2
Source dir: C:/Users/<USER>/Pictures/scrcpy/scrcpy-master
Build dir: C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native
Build type: native build
Project name: scrcpy
Project version: 3.3.1
-----------
Detecting compiler via: `icl ""` -> [WinError 2] The system cannot find the file specified
-----------
Detecting compiler via: `cl /?` -> [WinError 2] The system cannot find the file specified
-----------
Detecting compiler via: `cc --version` -> 0
stdout:
cc (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
-----------
Running command: -cpp -x c -E -dM -
-----
-----------
Detecting linker via: `cc -Wl,--version` -> 0
stdout:
GNU ld (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) a later version.
This program has absolutely no warranty.
-----------
stderr:
collect2 version 15.1.0
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\msys64\tmp\ccuiOzt6.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. --version -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
-----------
Sanity testing C compiler: cc
Is cross compiler: False.
Sanity check compiler command line: cc sanitycheckc.c -o sanitycheckc.exe -D_FILE_OFFSET_BITS=64
Sanity check compile stdout:

-----
Sanity check compile stderr:

-----
Running test binary command:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/sanitycheckc.exe
C compiler for the host machine: cc (gcc 15.1.0 "cc (Rev6, Built by MSYS2 project) 15.1.0")
C linker for the host machine: cc ld.bfd 2.44
-----------
Detecting archiver via: `gcc-ar --version` -> 0
stdout:
GNU ar (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) any later version.
This program has absolutely no warranty.
-----------
-----------
Detecting compiler via: `icl ""` -> [WinError 2] The system cannot find the file specified
-----------
Detecting compiler via: `cl /?` -> [WinError 2] The system cannot find the file specified
-----------
Detecting compiler via: `cc --version` -> 0
stdout:
cc (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
-----------
Running command: -cpp -x c -E -dM -
-----
-----------
Detecting linker via: `cc -Wl,--version` -> 0
stdout:
GNU ld (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) a later version.
This program has absolutely no warranty.
-----------
stderr:
collect2 version 15.1.0
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\msys64\tmp\cctjFeOk.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. --version -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
-----------
Sanity testing C compiler: cc
Is cross compiler: False.
Sanity check compiler command line: cc sanitycheckc.c -o sanitycheckc.exe -D_FILE_OFFSET_BITS=64
Sanity check compile stdout:

-----
Sanity check compile stderr:

-----
Running test binary command:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/sanitycheckc.exe
C compiler for the build machine: cc (gcc 15.1.0 "cc (Rev6, Built by MSYS2 project) 15.1.0")
C linker for the build machine: cc ld.bfd 2.44
-----------
Detecting archiver via: `gcc-ar --version` -> 0
stdout:
GNU ar (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) any later version.
This program has absolutely no warranty.
-----------
Build machine cpu family: x86_64
Build machine cpu: x86_64
Host machine cpu family: x86_64
Host machine cpu: x86_64
Target machine cpu family: x86_64
Target machine cpu: x86_64
Windows resource compiler: GNU windres (GNU Binutils) 2.44
Pkg-config binary missing from cross or native file, or env var undefined.
Trying a default Pkg-config fallback at pkg-config
Found pkg-config: YES (C:\msys64\mingw64\bin/pkg-config.EXE) 2.5.1
Determining dependency 'libavformat' with pkg-config executable 'C:\\msys64\\mingw64\\bin/pkg-config.EXE'
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --modversion libavformat` -> 1
stderr:
Package libavformat was not found in the pkg-config search path.
Perhaps you should add the directory containing `libavformat.pc'
to the PKG_CONFIG_PATH environment variable
Package 'libavformat' not found
-----------
CMake binary for host machine is not cached
CMake binary missing from cross or native file, or env var undefined.
Trying a default CMake fallback at cmake
Found CMake: C:\msys64\mingw64\bin/cmake.EXE (4.0.3)
Extracting basic cmake information
CMake Toolchain: Calling CMake once to generate the compiler state
Calling CMake (['C:\\msys64\\mingw64\\bin/cmake.EXE']) in C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/__CMake_compiler_info__ with:
  - "--trace-expand"
  - "--trace-format=json-v1"
  - "--no-warn-unused-cli"
  - "--trace-redirect=cmake_trace.txt"
  - "-G"
  - "Ninja"
  - "-DCMAKE_TOOLCHAIN_FILE=C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/__CMake_compiler_info__/CMakeMesonTempToolchainFile.cmake"
  - "."
