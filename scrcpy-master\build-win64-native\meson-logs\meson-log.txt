Build started at 2025-07-08T14:40:15.671913
Main binary: C:/msys64/mingw64/bin/python.exe
Build Options: 
Python system: Windows
The Meson build system
Version: 1.8.2
Source dir: C:/Users/<USER>/Pictures/scrcpy/scrcpy-master
Build dir: C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native
Build type: native build
Project name: scrcpy
Project version: 3.3.1
-----------
Detecting compiler via: `icl ""` -> [WinError 2] The system cannot find the file specified
-----------
Detecting compiler via: `cl /?` -> [WinError 2] The system cannot find the file specified
-----------
Detecting compiler via: `cc --version` -> 0
stdout:
cc (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
-----------
Running command: -cpp -x c -E -dM -
-----
-----------
Detecting linker via: `cc -Wl,--version` -> 0
stdout:
GNU ld (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) a later version.
This program has absolutely no warranty.
-----------
stderr:
collect2 version 15.1.0
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\msys64\tmp\ccFMqVB2.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. --version -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
-----------
Sanity testing C compiler: cc
Is cross compiler: False.
Sanity check compiler command line: cc sanitycheckc.c -o sanitycheckc.exe -D_FILE_OFFSET_BITS=64
Sanity check compile stdout:

-----
Sanity check compile stderr:

-----
Running test binary command:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/sanitycheckc.exe
C compiler for the host machine: cc (gcc 15.1.0 "cc (Rev6, Built by MSYS2 project) 15.1.0")
C linker for the host machine: cc ld.bfd 2.44
-----------
Detecting archiver via: `gcc-ar --version` -> 0
stdout:
GNU ar (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) any later version.
This program has absolutely no warranty.
-----------
-----------
Detecting compiler via: `icl ""` -> [WinError 2] The system cannot find the file specified
-----------
Detecting compiler via: `cl /?` -> [WinError 2] The system cannot find the file specified
-----------
Detecting compiler via: `cc --version` -> 0
stdout:
cc (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
-----------
Running command: -cpp -x c -E -dM -
-----
-----------
Detecting linker via: `cc -Wl,--version` -> 0
stdout:
GNU ld (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) a later version.
This program has absolutely no warranty.
-----------
stderr:
collect2 version 15.1.0
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\msys64\tmp\ccgzr05j.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. --version -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
-----------
Sanity testing C compiler: cc
Is cross compiler: False.
Sanity check compiler command line: cc sanitycheckc.c -o sanitycheckc.exe -D_FILE_OFFSET_BITS=64
Sanity check compile stdout:

-----
Sanity check compile stderr:

-----
Running test binary command:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/sanitycheckc.exe
C compiler for the build machine: cc (gcc 15.1.0 "cc (Rev6, Built by MSYS2 project) 15.1.0")
C linker for the build machine: cc ld.bfd 2.44
-----------
Detecting archiver via: `gcc-ar --version` -> 0
stdout:
GNU ar (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) any later version.
This program has absolutely no warranty.
-----------
Build machine cpu family: x86_64
Build machine cpu: x86_64
Host machine cpu family: x86_64
Host machine cpu: x86_64
Target machine cpu family: x86_64
Target machine cpu: x86_64
Windows resource compiler: GNU windres (GNU Binutils) 2.44
Pkg-config binary missing from cross or native file, or env var undefined.
Trying a default Pkg-config fallback at pkg-config
Found pkg-config: YES (C:\msys64\mingw64\bin/pkg-config.EXE) 2.5.1
Determining dependency 'libavformat' with pkg-config executable 'C:\\msys64\\mingw64\\bin/pkg-config.EXE'
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --modversion libavformat` -> 0
stdout:
61.7.100
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --cflags libavformat` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libavformat` -> 0
stdout:
-LC:/msys64/mingw64/lib -lavformat
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libavformat` -> 0
stdout:
-lavformat
-----------
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpujuwd1i7
Code:

        #include<stddef.h>
        #include<stdio.h>
        int main(void) {
            printf("%ld\n", (long)(sizeof(void *)));
            return 0;
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpujuwd1i7/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpujuwd1i7/output.exe -D_FILE_OFFSET_BITS=64 -O0` -> 0
Program stdout:

8

Program stderr:


Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp_2fuy10f
Code:

-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp_2fuy10f/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp_2fuy10f/output.obj -c -D_FILE_OFFSET_BITS=64 -O0 --print-search-dirs` -> 0
stdout:
install: C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/
programs: =C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
libraries: =C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;D:/M/msys64/mingw64/lib/x86_64-w64-mingw32/15.1.0/;D:/M/msys64/mingw64/lib/../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../;D:/M/msys64/mingw64/lib/
-----------
Run-time dependency libavformat found: YES 61.7.100
Determining dependency 'libavcodec' with pkg-config executable 'C:\\msys64\\mingw64\\bin/pkg-config.EXE'
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --modversion libavcodec` -> 0
stdout:
61.19.101
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --cflags libavcodec` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libavcodec` -> 0
stdout:
-LC:/msys64/mingw64/lib -lavcodec
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libavcodec` -> 0
stdout:
-lavcodec
-----------
Run-time dependency libavcodec found: YES 61.19.101
Determining dependency 'libavutil' with pkg-config executable 'C:\\msys64\\mingw64\\bin/pkg-config.EXE'
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --modversion libavutil` -> 0
stdout:
59.39.100
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --cflags libavutil` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libavutil` -> 0
stdout:
-LC:/msys64/mingw64/lib -lavutil
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libavutil` -> 0
stdout:
-lavutil
-----------
Run-time dependency libavutil found: YES 59.39.100
Determining dependency 'libswresample' with pkg-config executable 'C:\\msys64\\mingw64\\bin/pkg-config.EXE'
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --modversion libswresample` -> 0
stdout:
5.3.100
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --cflags libswresample` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libswresample` -> 0
stdout:
-LC:/msys64/mingw64/lib -lswresample
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libswresample` -> 0
stdout:
-lswresample
-----------
Run-time dependency libswresample found: YES 5.3.100
Determining dependency 'sdl2' with pkg-config executable 'C:\\msys64\\mingw64\\bin/pkg-config.EXE'
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --modversion sdl2` -> 0
stdout:
2.32.8
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --cflags sdl2` -> 0
stdout:
-IC:/msys64/mingw64/include/SDL2 -Dmain=SDL_main
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs sdl2` -> 0
stdout:
-LC:/msys64/mingw64/lib -lmingw32 -mwindows -lSDL2main -lSDL2
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs sdl2` -> 0
stdout:
-lmingw32 -mwindows -lSDL2main -lSDL2
-----------
Run-time dependency sdl2 found: YES 2.32.8
Determining dependency 'libusb-1.0' with pkg-config executable 'C:\\msys64\\mingw64\\bin/pkg-config.EXE'
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --modversion libusb-1.0` -> 0
stdout:
1.0.28
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --cflags libusb-1.0` -> 0
stdout:
-IC:/msys64/mingw64/include/libusb-1.0
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libusb-1.0` -> 0
stdout:
-LC:/msys64/mingw64/lib -lusb-1.0
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --libs libusb-1.0` -> 0
stdout:
-lusb-1.0
-----------
Run-time dependency libusb-1.0 found: YES 1.0.28
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmprlc0nh8v
Code:
int main(void) { return 0; }

-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmprlc0nh8v/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmprlc0nh8v/output.exe -D_FILE_OFFSET_BITS=64 -O0 -lmingw32 -Wl,--allow-shlib-undefined` -> 0
Library mingw32 found: YES
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp28guravm
Code:
int main(void) { return 0; }

-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp28guravm/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp28guravm/output.exe -D_FILE_OFFSET_BITS=64 -O0 -lws2_32 -Wl,--allow-shlib-undefined` -> 0
Library ws2_32 found: YES
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp2te7bsn0
Code:

        #define strdup meson_disable_define_of_strdup
        
        #include <limits.h>
        #undef strdup
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char strdup (void);
        
        #if defined __stub_strdup || defined __stub___strdup
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return strdup ();
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp2te7bsn0/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp2te7bsn0/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 0
Checking for function "strdup" : YES 
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp4dvqbxsa
Code:

        #define asprintf meson_disable_define_of_asprintf
        
        #include <limits.h>
        #undef asprintf
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char asprintf (void);
        
        #if defined __stub_asprintf || defined __stub___asprintf
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return asprintf ();
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp4dvqbxsa/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmp4dvqbxsa/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 0
Checking for function "asprintf" : YES 
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmptr_5eepa
Code:

        #define vasprintf meson_disable_define_of_vasprintf
        
        #include <limits.h>
        #undef vasprintf
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char vasprintf (void);
        
        #if defined __stub_vasprintf || defined __stub___vasprintf
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return vasprintf ();
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmptr_5eepa/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmptr_5eepa/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 0
Checking for function "vasprintf" : YES 
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpt107ajej
Code:

        #define nrand48 meson_disable_define_of_nrand48
        
        #include <limits.h>
        #undef nrand48
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char nrand48 (void);
        
        #if defined __stub_nrand48 || defined __stub___nrand48
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return nrand48 ();
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpt107ajej/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpt107ajej/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 1
stderr:
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: C:\msys64\tmp\cc09Qaie.o:testfile.c:(.text+0xe): undefined reference to `nrand48'
collect2.exe: error: ld returned 1 exit status
-----------
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpucpyvenu
Code:

        int main(void) {

        /* With some toolchains (MSYS2/mingw for example) the compiler
         * provides various builtins which are not really implemented and
         * fall back to the stdlib where they aren't provided and fail at
         * build/link time. In case the user provides a header, including
         * the header didn't lead to the function being defined, and the
         * function we are checking isn't a builtin itself we assume the
         * builtin is not functional and we just error out. */
        #if !1 && !defined(nrand48) && !0
            #error "No definition for __builtin_nrand48 found in the prefix"
        #endif

        #ifdef __has_builtin
            #if !__has_builtin(__builtin_nrand48)
                #error "__builtin_nrand48 not found"
            #endif
        #elif ! defined(nrand48)
            __builtin_nrand48;
        #endif
        return 0;
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpucpyvenu/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpucpyvenu/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 1
stderr:
C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpucpyvenu/testfile.c: In function 'main':
C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpucpyvenu/testfile.c:17:18: error: #error "__builtin_nrand48 not found"
   17 |                 #error "__builtin_nrand48 not found"
      |                  ^~~~~
-----------
Checking for function "nrand48" : NO 
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmphe2hz2xp
Code:

        #define jrand48 meson_disable_define_of_jrand48
        
        #include <limits.h>
        #undef jrand48
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char jrand48 (void);
        
        #if defined __stub_jrand48 || defined __stub___jrand48
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return jrand48 ();
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmphe2hz2xp/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmphe2hz2xp/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 1
stderr:
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: C:\msys64\tmp\cc4sWrBl.o:testfile.c:(.text+0xe): undefined reference to `jrand48'
collect2.exe: error: ld returned 1 exit status
-----------
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpmo1xn34t
Code:

        int main(void) {

        /* With some toolchains (MSYS2/mingw for example) the compiler
         * provides various builtins which are not really implemented and
         * fall back to the stdlib where they aren't provided and fail at
         * build/link time. In case the user provides a header, including
         * the header didn't lead to the function being defined, and the
         * function we are checking isn't a builtin itself we assume the
         * builtin is not functional and we just error out. */
        #if !1 && !defined(jrand48) && !0
            #error "No definition for __builtin_jrand48 found in the prefix"
        #endif

        #ifdef __has_builtin
            #if !__has_builtin(__builtin_jrand48)
                #error "__builtin_jrand48 not found"
            #endif
        #elif ! defined(jrand48)
            __builtin_jrand48;
        #endif
        return 0;
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpmo1xn34t/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpmo1xn34t/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 1
stderr:
C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpmo1xn34t/testfile.c: In function 'main':
C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpmo1xn34t/testfile.c:17:18: error: #error "__builtin_jrand48 not found"
   17 |                 #error "__builtin_jrand48 not found"
      |                  ^~~~~
-----------
Checking for function "jrand48" : NO 
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpy8iwrobc
Code:

        #define reallocarray meson_disable_define_of_reallocarray
        
        #include <limits.h>
        #undef reallocarray
        
        #ifdef __cplusplus
        extern "C"
        #endif
        char reallocarray (void);
        
        #if defined __stub_reallocarray || defined __stub___reallocarray
        fail fail fail this function is not going to work
        #endif
        
        int main(void) {
          return reallocarray ();
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpy8iwrobc/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpy8iwrobc/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 1
stderr:
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe: C:\msys64\tmp\ccOUi77Q.o:testfile.c:(.text+0xe): undefined reference to `reallocarray'
collect2.exe: error: ld returned 1 exit status
-----------
Running compile:
Working directory:  C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpaxhd3x80
Code:

        int main(void) {

        /* With some toolchains (MSYS2/mingw for example) the compiler
         * provides various builtins which are not really implemented and
         * fall back to the stdlib where they aren't provided and fail at
         * build/link time. In case the user provides a header, including
         * the header didn't lead to the function being defined, and the
         * function we are checking isn't a builtin itself we assume the
         * builtin is not functional and we just error out. */
        #if !1 && !defined(reallocarray) && !0
            #error "No definition for __builtin_reallocarray found in the prefix"
        #endif

        #ifdef __has_builtin
            #if !__has_builtin(__builtin_reallocarray)
                #error "__builtin_reallocarray not found"
            #endif
        #elif ! defined(reallocarray)
            __builtin_reallocarray;
        #endif
        return 0;
        }
-----------
Command line: `cc C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpaxhd3x80/testfile.c -o C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpaxhd3x80/output.exe -D_FILE_OFFSET_BITS=64 -O0 -std=c11 -Wl,--start-group -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -Wl,--end-group` -> 1
stderr:
C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpaxhd3x80/testfile.c: In function 'main':
C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/tmpaxhd3x80/testfile.c:17:18: error: #error "__builtin_reallocarray not found"
   17 |                 #error "__builtin_reallocarray not found"
      |                  ^~~~~
-----------
Checking for function "reallocarray" : NO 
Configuring config.h using configuration
Adding test "test_adb_parser"
Adding test "test_binary"
Adding test "test_audiobuf"
Adding test "test_cli"
Adding test "test_control_msg_serialize"
Adding test "test_device_msg_deserialize"
Adding test "test_orientation"
Adding test "test_strbuf"
Adding test "test_str"
Adding test "test_vecdeque"
Adding test "test_vector"
Program ./scripts/build-wrapper.sh found: YES (bash C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/server/./scripts/build-wrapper.sh)
Build targets in project: 14

env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --variable=bindir libavformat` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --print-variables libavformat` -> 0
stdout:
includedir
libdir
exec_prefix
prefix
orig_prefix
pcfiledir
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --variable=bindir libavcodec` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --print-variables libavcodec` -> 0
stdout:
includedir
libdir
exec_prefix
prefix
orig_prefix
pcfiledir
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --variable=bindir libavutil` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --print-variables libavutil` -> 0
stdout:
includedir
libdir
exec_prefix
prefix
orig_prefix
pcfiledir
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --variable=bindir libswresample` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --print-variables libswresample` -> 0
stdout:
includedir
libdir
exec_prefix
prefix
orig_prefix
pcfiledir
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --variable=bindir sdl2` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --print-variables sdl2` -> 0
stdout:
includedir
libdir
exec_prefix
prefix
orig_prefix
pcfiledir
-----------
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --variable=bindir libusb-1.0` -> 0
env[PKG_CONFIG_PATH]: C:\msys64\mingw64\lib\pkgconfig;C:\msys64\mingw64\share\pkgconfig
env[PKG_CONFIG_SYSTEM_INCLUDE_PATH]: C:/msys64/mingw64/include
env[PKG_CONFIG_SYSTEM_LIBRARY_PATH]: C:/msys64/mingw64/lib
env[PKG_CONFIG]: C:\msys64\mingw64\bin/pkg-config.EXE
-----------
Called: `C:\msys64\mingw64\bin/pkg-config.EXE --print-variables libusb-1.0` -> 0
stdout:
includedir
libdir
exec_prefix
prefix
orig_prefix
pcfiledir
-----------
Found ninja-1.13.0 at C:\msys64\mingw64\bin/ninja.EXE
Failed to guess install tag for C:/msys64/mingw64/share/scrcpy/scrcpy-server
Failed to guess install tag for C:/msys64/mingw64/share/icons/hicolor/256x256/apps/scrcpy.png
Failed to guess install tag for C:/msys64/mingw64/share/zsh/site-functions/_scrcpy
Failed to guess install tag for C:/msys64/mingw64/share/bash-completion/completions/scrcpy
Failed to guess install tag for C:/msys64/mingw64/share/scrcpy/scrcpy-server
Failed to guess install tag for C:/msys64/mingw64/share/icons/hicolor/256x256/apps/scrcpy.png
Failed to guess install tag for C:/msys64/mingw64/share/zsh/site-functions/_scrcpy
Failed to guess install tag for C:/msys64/mingw64/share/bash-completion/completions/scrcpy
