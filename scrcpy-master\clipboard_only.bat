@echo off
REM Chạy scrcpy trong clipboard-only mode

echo ========================================
echo Scrcpy Clipboard-Only Mode
echo ========================================
echo.
echo Chế độ này chỉ đồng bộ clipboard, không có video/audio
echo Tiêu tốn ít tài nguyên, phù hợp chạy background
echo.
echo Tính năng:
echo - Windows → Android: Copy trên Windows, tự động sync tới Android
echo - Android → Windows: Copy trên Android, tự động sync tới Windows
echo - Không cần nhấn Ctrl+V
echo.
echo Nhấn Ctrl+C để thoát
echo.

REM Thêm ADB vào PATH nếu cần
if exist "D:\Android\GT_Neo2\platform-tools\adb.exe" (
    set "PATH=%PATH%;D:\Android\GT_Neo2\platform-tools"
)

REM Chạy scrcpy clipboard-only mode
build-win64-native\app\scrcpy.exe --clipboard-only

echo.
echo Clipboard sync đã dừng
pause
