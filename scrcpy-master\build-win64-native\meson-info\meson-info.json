{"meson_version": {"full": "1.8.2", "major": 1, "minor": 8, "patch": 2}, "directories": {"source": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master", "build": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native", "info": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-info"}, "introspection": {"version": {"full": "1.0.0", "major": 1, "minor": 0, "patch": 0}, "information": {"benchmarks": {"file": "intro-benchmarks.json", "updated": true}, "buildoptions": {"file": "intro-buildoptions.json", "updated": true}, "buildsystem_files": {"file": "intro-buildsystem_files.json", "updated": true}, "compilers": {"file": "intro-compilers.json", "updated": true}, "dependencies": {"file": "intro-dependencies.json", "updated": true}, "installed": {"file": "intro-installed.json", "updated": true}, "install_plan": {"file": "intro-install_plan.json", "updated": true}, "machines": {"file": "intro-machines.json", "updated": true}, "projectinfo": {"file": "intro-projectinfo.json", "updated": true}, "targets": {"file": "intro-targets.json", "updated": true}, "tests": {"file": "intro-tests.json", "updated": true}}}, "build_files_updated": true, "error": false}