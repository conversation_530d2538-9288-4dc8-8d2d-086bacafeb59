{"meson_version": {"full": "1.8.2", "major": 1, "minor": 8, "patch": 2}, "directories": {"source": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master", "build": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native", "info": "C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-info"}, "introspection": {"version": {"full": "1.0.0", "major": 1, "minor": 0, "patch": 0}, "information": {"benchmarks": {"file": "intro-benchmarks.json", "updated": false}, "buildoptions": {"file": "intro-buildoptions.json", "updated": false}, "buildsystem_files": {"file": "intro-buildsystem_files.json", "updated": false}, "compilers": {"file": "intro-compilers.json", "updated": false}, "dependencies": {"file": "intro-dependencies.json", "updated": false}, "installed": {"file": "intro-installed.json", "updated": false}, "install_plan": {"file": "intro-install_plan.json", "updated": false}, "machines": {"file": "intro-machines.json", "updated": false}, "projectinfo": {"file": "intro-projectinfo.json", "updated": false}, "targets": {"file": "intro-targets.json", "updated": false}, "tests": {"file": "intro-tests.json", "updated": false}}}, "build_files_updated": false, "error": true, "error_list": ["Dependency \"libavformat\" not found, tried pkgconfig and cmake"]}