Build started at 2025-07-08T14:34:11.778304
Main binary: C:/msys64/mingw64/bin/python.exe
Build Options: --cross-file=cross_win64.txt
Python system: Windows
The Meson build system
Version: 1.8.2
Source dir: C:/Users/<USER>/Pictures/scrcpy/scrcpy-master
Build dir: C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64
Build type: cross build
Project name: scrcpy
Project version: 3.3.1
-----------
Detecting compiler via: `x86_64-w64-mingw32-gcc --version` -> 0
stdout:
x86_64-w64-mingw32-gcc (Rev6, Built by MSYS2 project) 15.1.0
Copyright (C) 2025 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
-----------
Running command: -cpp -x c -E -dM -
-----
-----------
Detecting linker via: `x86_64-w64-mingw32-gcc -Wl,--version` -> 0
stdout:
GNU ld (GNU Binutils) 2.44
Copyright (C) 2025 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) a later version.
This program has absolutely no warranty.
-----------
stderr:
collect2 version 15.1.0
C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\msys64\tmp\cc79JuHE.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. --version -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
-----------
Sanity testing C compiler: x86_64-w64-mingw32-gcc
Is cross compiler: True.
Sanity check compiler command line: x86_64-w64-mingw32-gcc sanitycheckc.c -o sanitycheckc_cross.exe -D_FILE_OFFSET_BITS=64 -c
Sanity check compile stdout:

-----
Sanity check compile stderr:

-----
C compiler for the host machine: x86_64-w64-mingw32-gcc (gcc 15.1.0 "x86_64-w64-mingw32-gcc (Rev6, Built by MSYS2 project) 15.1.0")
C linker for the host machine: x86_64-w64-mingw32-gcc ld.bfd 2.44
-----------
Detecting archiver via: `x86_64-w64-mingw32-ar --version` -> [WinError 2] The system cannot find the file specified

meson.build:1:0: ERROR: Unknown linker(s): [['x86_64-w64-mingw32-ar']]
The following exception(s) were encountered:
Running `x86_64-w64-mingw32-ar --version` gave "[WinError 2] The system cannot find the file specified"
