{"version":{"major":1,"minor":2}}
{"args":["VERSION","4.0.3"],"cmd":"cmake_minimum_required","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":2,"time":1751960211.5062466}
{"args":["MesonTemp","LANGUAGES","C"],"cmd":"project","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":3,"time":1751960211.5062466}
{"args":["CMAKE_HOST_UNIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":11,"time":1751960211.5072498}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":127,"time":1751960211.5072498}
{"args":["CMAKE_HOST_WIN32"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":128,"time":1751960211.5072498}
{"args":["DEFINED","ENV{PROCESSOR_ARCHITEW6432}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":129,"time":1751960211.5072498}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":131,"time":1751960211.5082569}
{"args":["CMAKE_HOST_SYSTEM_PROCESSOR","AMD64"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":132,"time":1751960211.5082569}
{"args":["CMAKE_TOOLCHAIN_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":141,"time":1751960211.5082569}
{"args":["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","OPTIONAL","RESULT_VARIABLE","_INCLUDED_TOOLCHAIN_FILE"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":143,"time":1751960211.5082569}
{"args":["NOT","_INCLUDED_TOOLCHAIN_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":144,"time":1751960211.5082569}
{"args":["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","OPTIONAL","RESULT_VARIABLE","_INCLUDED_TOOLCHAIN_FILE"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":146,"time":1751960211.5082569}
{"args":["DEFINED","MESON_PRELOAD_FILE"],"cmd":"if","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":9,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER","C:/msys64/mingw64/bin/cc.EXE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":15,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER_ID","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":16,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER_VERSION","15.1.0"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":17,"time":1751960211.5376358}
{"args":["CMAKE_C_STANDARD_COMPUTED_DEFAULT","23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":18,"time":1751960211.5376358}
{"args":["CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT","ON"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":19,"time":1751960211.5376358}
{"args":["CMAKE_C_PLATFORM_ID","MinGW"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":20,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":21,"time":1751960211.5376358}
{"args":["CMAKE_AR","C:/msys64/mingw64/bin/ar.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":22,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER_AR","C:/msys64/mingw64/bin/gcc-ar.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":23,"time":1751960211.5376358}
{"args":["CMAKE_RANLIB","C:/msys64/mingw64/bin/ranlib.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":24,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER_RANLIB","C:/msys64/mingw64/bin/gcc-ranlib.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":25,"time":1751960211.5376358}
{"args":["CMAKE_LINKER","C:/msys64/mingw64/bin/ld.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":26,"time":1751960211.5376358}
{"args":["CMAKE_TAPI","CMAKE_TAPI-NOTFOUND"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":27,"time":1751960211.5376358}
{"args":["CMAKE_COMPILER_IS_GNUCC","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":28,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER_LOADED","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":29,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER_ENV_VAR","CC"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":30,"time":1751960211.5376358}
{"args":["CMAKE_C_COMPILER_ID_RUN","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":31,"time":1751960211.5386696}
{"args":["CMAKE_C_SOURCE_FILE_EXTENSIONS","c","m"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":32,"time":1751960211.5386696}
{"args":["CMAKE_C_IGNORE_EXTENSIONS","h","H","o","O","obj","OBJ","def","DEF","rc","RC"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":33,"time":1751960211.5386696}
{"args":["CMAKE_C_LINKER_PREFERENCE","10"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":34,"time":1751960211.5386696}
{"args":["CMAKE_C_STANDARD_LATEST","23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":35,"time":1751960211.5386696}
{"args":["CMAKE_C_COMPILE_FEATURES","c_std_90","c_function_prototypes","c_std_99","c_restrict","c_variadic_macros","c_std_11","c_static_assert","c_std_17","c_std_23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":36,"time":1751960211.5386696}
{"args":["CMAKE_C90_COMPILE_FEATURES","c_std_90","c_function_prototypes"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":37,"time":1751960211.5386696}
{"args":["CMAKE_C99_COMPILE_FEATURES","c_std_99","c_restrict","c_variadic_macros"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":38,"time":1751960211.5386696}
{"args":["CMAKE_C11_COMPILE_FEATURES","c_std_11","c_static_assert"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":39,"time":1751960211.5386696}
{"args":["CMAKE_C17_COMPILE_FEATURES","c_std_17"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":40,"time":1751960211.5386696}
{"args":["CMAKE_C23_COMPILE_FEATURES","c_std_23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":41,"time":1751960211.5386696}
{"args":["CMAKE_C_COMPILER_LINKER","C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":42,"time":1751960211.5386696}
{"args":["CMAKE_C_COMPILER_LINKER_ID","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":43,"time":1751960211.5386696}
{"args":["CMAKE_C_COMPILER_LINKER_VERSION","2.44"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":44,"time":1751960211.5386696}
{"args":["CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":45,"time":1751960211.5386696}
{"args":["CMAKE_C_COMPILER_WORKS","TRUE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":46,"time":1751960211.5386696}
{"args":["CMAKE_C_ABI_COMPILED","TRUE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":47,"time":1751960211.5386696}
{"args":["CMAKE_C_SIZEOF_DATA_PTR","8"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":48,"time":1751960211.5386696}
{"args":["CMAKE_C_BYTE_ORDER","LITTLE_ENDIAN"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":49,"time":1751960211.5386696}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":50,"time":1751960211.5386696}
{"args":["CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES","C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include","C:/msys64/mingw64/include","C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":51,"time":1751960211.5386696}
{"args":["CMAKE_C_IMPLICIT_LINK_LIBRARIES","mingw32","gcc","mingwex","kernel32","pthread","advapi32","shell32","user32","kernel32","mingw32","gcc","mingwex","kernel32"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":52,"time":1751960211.5386696}
{"args":["CMAKE_C_IMPLICIT_LINK_DIRECTORIES","C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0","C:/msys64/mingw64/lib/gcc","C:/msys64/mingw64/x86_64-w64-mingw32/lib","C:/msys64/mingw64/lib"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":53,"time":1751960211.5386696}
{"args":["CMAKE_C_COMPILER_FORCED","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":54,"time":1751960211.5386696}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":58,"time":1751960211.5386696}
{"args":["CMAKE_C_COMPILER","C:/msys64/mingw64/bin/cc.EXE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":59,"time":1751960211.5386696}
{"args":["_INCLUDED_TOOLCHAIN_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":149,"time":1751960211.5386696}
{"args":["CMAKE_TOOLCHAIN_FILE","C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","CACHE","FILEPATH","The CMake toolchain file","FORCE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":150,"time":1751960211.5386696}
{"args":["CMAKE_SYSTEM_NAME"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":156,"time":1751960211.5386696}
{"args":["CMAKE_VS_WINCE_VERSION"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":162,"time":1751960211.5386696}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":167,"time":1751960211.5386696}
{"args":["CMAKE_SYSTEM_NAME","Windows"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":169,"time":1751960211.5386696}
{"args":["NOT","DEFINED","CMAKE_SYSTEM_VERSION"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":170,"time":1751960211.5386696}
{"args":["CMAKE_SYSTEM_VERSION","10.0.26100"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":171,"time":1751960211.5386696}
{"args":["CMAKE_SYSTEM_PROCESSOR","AMD64"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":173,"time":1751960211.5386696}
{"args":["CMAKE_CROSSCOMPILING"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":174,"time":1751960211.5386696}
{"args":["CMAKE_CROSSCOMPILING","FALSE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":181,"time":1751960211.5386696}
{"args":["Platform/Windows-Determine","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":184,"time":1751960211.5386696}
{"args":["CMAKE_SYSTEM","Windows"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":186,"time":1751960211.5386696}
{"args":["CMAKE_SYSTEM_VERSION"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":187,"time":1751960211.53965}
{"args":["APPEND","CMAKE_SYSTEM","-10.0.26100"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":188,"time":1751960211.53965}
{"args":["CMAKE_HOST_SYSTEM","Windows"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":190,"time":1751960211.53965}
{"args":["CMAKE_HOST_SYSTEM_VERSION"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":191,"time":1751960211.53965}
{"args":["APPEND","CMAKE_HOST_SYSTEM","-10.0.26100"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":192,"time":1751960211.53965}
{"args":["CMAKE_BINARY_DIR"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":197,"time":1751960211.53965}
{"args":["CMAKE_CROSSCOMPILING"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":199,"time":1751960211.53965}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":204,"time":1751960211.53965}
{"args":["CONFIGURE_LOG","The system is: Windows - 10.0.26100 - AMD64\n"],"cmd":"message","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":205,"line_end":207,"time":1751960211.53965}
{"args":["INCLUDE_CMAKE_TOOLCHAIN_FILE_IF_REQUIRED"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":212,"time":1751960211.53965}
{"args":["CMAKE_TOOLCHAIN_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":213,"time":1751960211.53965}
{"args":["INCLUDE_CMAKE_TOOLCHAIN_FILE_IF_REQUIRED","include(\"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake\")"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":214,"time":1751960211.53965}
{"args":["C:/msys64/mingw64/share/cmake/Modules/CMakeSystem.cmake.in","C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","@ONLY"],"cmd":"configure_file","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake","frame":2,"global_frame":2,"line":218,"line_end":220,"time":1751960211.53965}
{"args":["CMAKE_HOST_SYSTEM","Windows-10.0.26100"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":1,"time":1751960211.5541744}
{"args":["CMAKE_HOST_SYSTEM_NAME","Windows"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":2,"time":1751960211.5541744}
{"args":["CMAKE_HOST_SYSTEM_VERSION","10.0.26100"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":3,"time":1751960211.5541744}
{"args":["CMAKE_HOST_SYSTEM_PROCESSOR","AMD64"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":4,"time":1751960211.5541744}
{"args":["C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake"],"cmd":"include","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":6,"time":1751960211.5541744}
{"args":["DEFINED","MESON_PRELOAD_FILE"],"cmd":"if","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":9,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER","C:/msys64/mingw64/bin/cc.EXE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":15,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_ID","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":16,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_VERSION","15.1.0"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":17,"time":1751960211.5556812}
{"args":["CMAKE_C_STANDARD_COMPUTED_DEFAULT","23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":18,"time":1751960211.5556812}
{"args":["CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT","ON"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":19,"time":1751960211.5556812}
{"args":["CMAKE_C_PLATFORM_ID","MinGW"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":20,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":21,"time":1751960211.5556812}
{"args":["CMAKE_AR","C:/msys64/mingw64/bin/ar.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":22,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_AR","C:/msys64/mingw64/bin/gcc-ar.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":23,"time":1751960211.5556812}
{"args":["CMAKE_RANLIB","C:/msys64/mingw64/bin/ranlib.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":24,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_RANLIB","C:/msys64/mingw64/bin/gcc-ranlib.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":25,"time":1751960211.5556812}
{"args":["CMAKE_LINKER","C:/msys64/mingw64/bin/ld.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":26,"time":1751960211.5556812}
{"args":["CMAKE_TAPI","CMAKE_TAPI-NOTFOUND"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":27,"time":1751960211.5556812}
{"args":["CMAKE_COMPILER_IS_GNUCC","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":28,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_LOADED","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":29,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_ENV_VAR","CC"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":30,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_ID_RUN","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":31,"time":1751960211.5556812}
{"args":["CMAKE_C_SOURCE_FILE_EXTENSIONS","c","m"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":32,"time":1751960211.5556812}
{"args":["CMAKE_C_IGNORE_EXTENSIONS","h","H","o","O","obj","OBJ","def","DEF","rc","RC"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":33,"time":1751960211.5556812}
{"args":["CMAKE_C_LINKER_PREFERENCE","10"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":34,"time":1751960211.5556812}
{"args":["CMAKE_C_STANDARD_LATEST","23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":35,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILE_FEATURES","c_std_90","c_function_prototypes","c_std_99","c_restrict","c_variadic_macros","c_std_11","c_static_assert","c_std_17","c_std_23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":36,"time":1751960211.5556812}
{"args":["CMAKE_C90_COMPILE_FEATURES","c_std_90","c_function_prototypes"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":37,"time":1751960211.5556812}
{"args":["CMAKE_C99_COMPILE_FEATURES","c_std_99","c_restrict","c_variadic_macros"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":38,"time":1751960211.5556812}
{"args":["CMAKE_C11_COMPILE_FEATURES","c_std_11","c_static_assert"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":39,"time":1751960211.5556812}
{"args":["CMAKE_C17_COMPILE_FEATURES","c_std_17"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":40,"time":1751960211.5556812}
{"args":["CMAKE_C23_COMPILE_FEATURES","c_std_23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":41,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_LINKER","C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":42,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_LINKER_ID","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":43,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_LINKER_VERSION","2.44"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":44,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":45,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_WORKS","TRUE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":46,"time":1751960211.5556812}
{"args":["CMAKE_C_ABI_COMPILED","TRUE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":47,"time":1751960211.5556812}
{"args":["CMAKE_C_SIZEOF_DATA_PTR","8"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":48,"time":1751960211.5556812}
{"args":["CMAKE_C_BYTE_ORDER","LITTLE_ENDIAN"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":49,"time":1751960211.5556812}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":50,"time":1751960211.5556812}
{"args":["CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES","C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include","C:/msys64/mingw64/include","C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":51,"time":1751960211.5556812}
{"args":["CMAKE_C_IMPLICIT_LINK_LIBRARIES","mingw32","gcc","mingwex","kernel32","pthread","advapi32","shell32","user32","kernel32","mingw32","gcc","mingwex","kernel32"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":52,"time":1751960211.5556812}
{"args":["CMAKE_C_IMPLICIT_LINK_DIRECTORIES","C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0","C:/msys64/mingw64/lib/gcc","C:/msys64/mingw64/x86_64-w64-mingw32/lib","C:/msys64/mingw64/lib"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":53,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER_FORCED","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":54,"time":1751960211.5556812}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":58,"time":1751960211.5556812}
{"args":["CMAKE_C_COMPILER","C:/msys64/mingw64/bin/cc.EXE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeMesonToolchainFile.cmake","frame":3,"global_frame":3,"line":59,"time":1751960211.5556812}
{"args":["CMAKE_SYSTEM","Windows-10.0.26100"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":8,"time":1751960211.5556812}
{"args":["CMAKE_SYSTEM_NAME","Windows"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":9,"time":1751960211.5556812}
{"args":["CMAKE_SYSTEM_VERSION","10.0.26100"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":10,"time":1751960211.5556812}
{"args":["CMAKE_SYSTEM_PROCESSOR","AMD64"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":11,"time":1751960211.5556812}
{"args":["CMAKE_CROSSCOMPILING","FALSE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":13,"time":1751960211.5556812}
{"args":["CMAKE_SYSTEM_LOADED","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeSystem.cmake","frame":2,"global_frame":2,"line":15,"time":1751960211.5556812}
{"args":["CMAKE_MAKE_PROGRAM","NAMES","ninja-build","ninja","samu","NAMES_PER_DIR","DOC","Program used to build from build.ninja files."],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeNinjaFindMake.cmake","frame":2,"global_frame":2,"line":5,"line_end":8,"time":1751960211.5566978}
{"args":["CMAKE_MAKE_PROGRAM"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeNinjaFindMake.cmake","frame":2,"global_frame":2,"line":9,"time":1751960211.5576992}
{"args":["APPLE"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":13,"time":1751960211.5779076}
{"args":["UNIX"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":14,"time":1751960211.5779076}
{"args":["CYGWIN"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":15,"time":1751960211.5779076}
{"args":["MSYS"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":16,"time":1751960211.5779076}
{"args":["WIN32"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":17,"time":1751960211.5779076}
{"args":["BSD"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":18,"time":1751960211.5779076}
{"args":["LINUX"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":19,"time":1751960211.5779076}
{"args":["AIX"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":20,"time":1751960211.5779076}
{"args":["CMAKE_EFFECTIVE_SYSTEM_NAME","Windows"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":33,"time":1751960211.5779076}
{"args":["Platform/Windows-Initialize","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":35,"time":1751960211.5779076}
{"args":["WIN32","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake","frame":3,"global_frame":3,"line":1,"time":1751960211.5794144}
{"args":["CMAKE_SYSTEM_SPECIFIC_INITIALIZE_LOADED","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake","frame":2,"global_frame":2,"line":37,"time":1751960211.5794144}
{"args":["C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":24,"time":1751960211.5794144}
{"args":["_cmake_find_compiler","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":5,"time":1751960211.5794144}
{"args":["_cmake_find_compiler_path","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":87,"time":1751960211.5794144}
{"args":["_cmake_find_compiler_sysroot","lang"],"cmd":"function","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":131,"time":1751960211.5794144}
{"args":["Platform/Windows-Determine-C","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":27,"time":1751960211.5794144}
{"args":["Platform/Windows-C","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":28,"time":1751960211.5794144}
{"args":["NOT","CMAKE_C_COMPILER_NAMES"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":29,"time":1751960211.5794144}
{"args":["CMAKE_C_COMPILER_NAMES","cc"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":30,"time":1751960211.5794144}
{"args":["Ninja","MATCHES","Visual Studio"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":33,"time":1751960211.5794144}
{"args":["Ninja","MATCHES","Green Hills MULTI"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":34,"time":1751960211.5794144}
{"args":["Ninja","MATCHES","Xcode"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":35,"time":1751960211.5794144}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":38,"time":1751960211.5794144}
{"args":["NOT","CMAKE_C_COMPILER"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":39,"time":1751960211.5794144}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":67,"time":1751960211.5794144}
{"args":["C"],"cmd":"_cmake_find_compiler_path","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":68,"time":1751960211.5794144}
{"args":["CMAKE_C_COMPILER"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":88,"time":1751960211.5794144}
{"args":["CONVERT","C:/msys64/mingw64/bin/cc.EXE","TO_CMAKE_PATH_LIST","CMAKE_C_COMPILER","NORMALIZE"],"cmd":"cmake_path","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":97,"time":1751960211.5804214}
{"args":["LENGTH","CMAKE_C_COMPILER","_CMAKE_C_COMPILER_LENGTH"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":99,"time":1751960211.5804214}
{"args":["_CMAKE_C_COMPILER_LENGTH","GREATER","1"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":100,"time":1751960211.5804214}
{"args":["_CMAKE_C_COMPILER_LENGTH"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":105,"time":1751960211.5804214}
{"args":["_CMAKE_USER_C_COMPILER_PATH","C:/msys64/mingw64/bin/cc.EXE","PATH"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":109,"time":1751960211.5804214}
{"args":["NOT","_CMAKE_USER_C_COMPILER_PATH"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":110,"time":1751960211.5804214}
{"args":["EXISTS","C:/msys64/mingw64/bin/cc.EXE"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":121,"time":1751960211.5804214}
{"args":["_CMAKE_C_COMPILER_CACHED","CACHE","CMAKE_C_COMPILER","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":122,"time":1751960211.5804214}
{"args":["_CMAKE_C_COMPILER_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":123,"time":1751960211.5804214}
{"args":["_CMAKE_C_COMPILER_CACHED"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake","frame":3,"global_frame":3,"line":126,"time":1751960211.5804214}
{"args":["CMAKE_C_COMPILER"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":70,"time":1751960211.5804214}
{"args":["CMAKE_C_COMPILER_ID_TEST_FLAGS_FIRST"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":75,"time":1751960211.5804214}
{"args":["CMAKE_C_COMPILER_ID_TEST_FLAGS","-c","-Aa","-D__CLASSIC_C__","--target=arm-arm-none-eabi -mcpu=cortex-m3","-c -I__does_not_exist__"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":76,"line_end":93,"time":1751960211.5804214}
{"args":["CMAKE_C_COMPILER_TARGET"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":95,"time":1751960211.5804214}
{"args":["NOT","CMAKE_C_COMPILER_ID_RUN"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":99,"time":1751960211.5804214}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":131,"time":1751960211.5804214}
{"args":["NOT","DEFINED","CMAKE_C_COMPILER_FRONTEND_VARIANT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":132,"time":1751960211.5804214}
{"args":["NOT","_CMAKE_TOOLCHAIN_LOCATION"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":150,"time":1751960211.5804214}
{"args":["_CMAKE_TOOLCHAIN_LOCATION","C:/msys64/mingw64/bin/cc.EXE","PATH"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":151,"time":1751960211.5804214}
{"args":["NOT","_CMAKE_TOOLCHAIN_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":161,"time":1751960211.5804214}
{"args":["CMAKE_C_COMPILER_ID","MATCHES","GNU|Clang|QCC|LCC"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":163,"time":1751960211.5815372}
{"args":["COMPILER_BASENAME","C:/msys64/mingw64/bin/cc.EXE","NAME"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":164,"time":1751960211.5815372}
{"args":["COMPILER_BASENAME","MATCHES","^(.+-)?(clang|g?cc)(-cl)?(-?[0-9]+(\\.[0-9]+)*)?(-[^.]+)?(\\.exe)?$"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":165,"time":1751960211.5815372}
{"args":["CMAKE_C_COMPILER_ID","MATCHES","TIClang"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":169,"time":1751960211.5815372}
{"args":["CMAKE_C_COMPILER_ID","MATCHES","Clang"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":174,"time":1751960211.5815372}
{"args":["COMPILER_BASENAME","MATCHES","qcc(\\.exe)?$"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":178,"time":1751960211.5815372}
{"args":["","MATCHES","(.+-)?llvm-$"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":186,"time":1751960211.5815372}
{"args":["_CMAKE_PROCESSING_LANGUAGE","C"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":200,"time":1751960211.5815372}
{"args":["CMakeFindBinUtils"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":201,"time":1751960211.5815372}
{"args":["__resolve_tool_path","CMAKE_TOOL","SEARCH_PATH","DOCSTRING"],"cmd":"function","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":25,"time":1751960211.5815372}
{"args":["CMAKE_LINKER","C:/msys64/mingw64/bin","Default Linker"],"cmd":"__resolve_tool_path","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":58,"time":1751960211.5815372}
{"args":["CMAKE_LINKER"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":4,"global_frame":4,"line":27,"time":1751960211.5815372}
{"args":["_CMAKE_USER_TOOL_PATH","C:/msys64/mingw64/bin/ld.exe","DIRECTORY"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.5815372}
{"args":["NOT","_CMAKE_USER_TOOL_PATH"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":4,"global_frame":4,"line":34,"time":1751960211.5815372}
{"args":["CMAKE_MT","C:/msys64/mingw64/bin","Default Manifest Tool"],"cmd":"__resolve_tool_path","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":59,"time":1751960211.5815372}
{"args":["CMAKE_MT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":4,"global_frame":4,"line":27,"time":1751960211.5815372}
{"args":["__resolve_linker_path","__linker_type","__name","__search_path","__doc"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":61,"time":1751960211.5825195}
{"args":["_CMAKE_TOOL_VARS",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":68,"time":1751960211.5825195}
{"args":["(","x","STREQUAL","xMSVC","AND","(","xGNU","STREQUAL","xMSVC","OR","NOT","xGNU","STREQUAL","xClang",")",")","OR","xGNU","STREQUAL","xMSVC","OR","(","CMAKE_HOST_WIN32","AND","xGNU","STREQUAL","xPGI",")","OR","(","CMAKE_HOST_WIN32","AND","xGNU","STREQUAL","xNVIDIA",")","OR","(","CMAKE_HOST_WIN32","AND","xC","STREQUAL","xISPC",")","OR","(","CMAKE_GENERATOR","MATCHES","Visual Studio","AND","NOT","CMAKE_VS_PLATFORM_NAME","STREQUAL","Tegra-Android",")"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":71,"line_end":79,"time":1751960211.5825195}
{"args":["xGNU","MATCHES","^x(Open)?Watcom$"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":106,"time":1751960211.5825195}
{"args":["xGNU","MATCHES","^xIAR$"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":111,"time":1751960211.5825195}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":153,"time":1751960211.5825195}
{"args":["CMAKE_C_COMPILER_EXTERNAL_TOOLCHAIN"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":154,"time":1751960211.5825195}
{"args":["CMAKE_CXX_COMPILER_EXTERNAL_TOOLCHAIN"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":157,"time":1751960211.5825195}
{"args":["_CMAKE_AR_NAMES","ar"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":162,"time":1751960211.5825195}
{"args":["_CMAKE_RANLIB_NAMES","ranlib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":163,"time":1751960211.5825195}
{"args":["_CMAKE_STRIP_NAMES","strip"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":164,"time":1751960211.5825195}
{"args":["_CMAKE_LINKER_NAMES","ld"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":165,"time":1751960211.5825195}
{"args":["_CMAKE_NM_NAMES","nm"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":166,"time":1751960211.5825195}
{"args":["_CMAKE_OBJDUMP_NAMES","objdump"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":167,"time":1751960211.5825195}
{"args":["_CMAKE_OBJCOPY_NAMES","objcopy"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":168,"time":1751960211.5825195}
{"args":["_CMAKE_READELF_NAMES","readelf"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":169,"time":1751960211.5825195}
{"args":["_CMAKE_DLLTOOL_NAMES","dlltool"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":170,"time":1751960211.5825195}
{"args":["_CMAKE_ADDR2LINE_NAMES","addr2line"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":171,"time":1751960211.5825195}
{"args":["_CMAKE_TAPI_NAMES","tapi"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":172,"time":1751960211.5825195}
{"args":["GNU","STREQUAL","Clang"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":175,"time":1751960211.5825195}
{"args":["GNU","STREQUAL","ARMClang"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":202,"time":1751960211.5825195}
{"args":["APPEND","_CMAKE_TOOL_VARS","AR","RANLIB","STRIP","LINKER","NM","OBJDUMP","OBJCOPY","READELF","DLLTOOL","ADDR2LINE","TAPI"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":207,"time":1751960211.5825195}
{"args":["_CMAKE_TOOL","IN","LISTS","_CMAKE_TOOL_VARS"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":210,"time":1751960211.5825195}
{"args":["_CMAKE_AR_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5825195}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_AR_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5825195}
{"args":["APPEND","_CMAKE_AR_FIND_NAMES","ar","ar","ar","ar"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5825195}
{"args":["REMOVE_DUPLICATES","_CMAKE_AR_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5825195}
{"args":["CMAKE_AR","NAMES","ar","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5825195}
{"args":["_CMAKE_AR_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5825195}
{"args":["_CMAKE_RANLIB_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5825195}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_RANLIB_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5825195}
{"args":["APPEND","_CMAKE_RANLIB_FIND_NAMES","ranlib","ranlib","ranlib","ranlib"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5825195}
{"args":["REMOVE_DUPLICATES","_CMAKE_RANLIB_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5825195}
{"args":["CMAKE_RANLIB","NAMES","ranlib","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5825195}
{"args":["_CMAKE_RANLIB_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5825195}
{"args":["_CMAKE_STRIP_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5825195}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_STRIP_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5825195}
{"args":["APPEND","_CMAKE_STRIP_FIND_NAMES","strip","strip","strip","strip"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5835342}
{"args":["REMOVE_DUPLICATES","_CMAKE_STRIP_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5835342}
{"args":["CMAKE_STRIP","NAMES","strip","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5835342}
{"args":["_CMAKE_STRIP_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5835342}
{"args":["_CMAKE_LINKER_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5835342}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_LINKER_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5835342}
{"args":["APPEND","_CMAKE_LINKER_FIND_NAMES","ld","ld","ld","ld"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5835342}
{"args":["REMOVE_DUPLICATES","_CMAKE_LINKER_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5835342}
{"args":["CMAKE_LINKER","NAMES","ld","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5835342}
{"args":["_CMAKE_LINKER_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5835342}
{"args":["_CMAKE_NM_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5835342}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_NM_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5835342}
{"args":["APPEND","_CMAKE_NM_FIND_NAMES","nm","nm","nm","nm"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5835342}
{"args":["REMOVE_DUPLICATES","_CMAKE_NM_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5835342}
{"args":["CMAKE_NM","NAMES","nm","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5835342}
{"args":["_CMAKE_NM_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5835342}
{"args":["_CMAKE_OBJDUMP_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5835342}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_OBJDUMP_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5835342}
{"args":["APPEND","_CMAKE_OBJDUMP_FIND_NAMES","objdump","objdump","objdump","objdump"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5835342}
{"args":["REMOVE_DUPLICATES","_CMAKE_OBJDUMP_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5835342}
{"args":["CMAKE_OBJDUMP","NAMES","objdump","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5835342}
{"args":["_CMAKE_OBJDUMP_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5845261}
{"args":["_CMAKE_OBJCOPY_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5845261}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_OBJCOPY_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5845261}
{"args":["APPEND","_CMAKE_OBJCOPY_FIND_NAMES","objcopy","objcopy","objcopy","objcopy"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5845261}
{"args":["REMOVE_DUPLICATES","_CMAKE_OBJCOPY_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5845261}
{"args":["CMAKE_OBJCOPY","NAMES","objcopy","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5845261}
{"args":["_CMAKE_OBJCOPY_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5845261}
{"args":["_CMAKE_READELF_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5845261}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_READELF_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5845261}
{"args":["APPEND","_CMAKE_READELF_FIND_NAMES","readelf","readelf","readelf","readelf"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5845261}
{"args":["REMOVE_DUPLICATES","_CMAKE_READELF_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5845261}
{"args":["CMAKE_READELF","NAMES","readelf","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5845261}
{"args":["_CMAKE_READELF_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5845261}
{"args":["_CMAKE_DLLTOOL_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5845261}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_DLLTOOL_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5845261}
{"args":["APPEND","_CMAKE_DLLTOOL_FIND_NAMES","dlltool","dlltool","dlltool","dlltool"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5845261}
{"args":["REMOVE_DUPLICATES","_CMAKE_DLLTOOL_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5845261}
{"args":["CMAKE_DLLTOOL","NAMES","dlltool","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5845261}
{"args":["_CMAKE_DLLTOOL_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5845261}
{"args":["_CMAKE_ADDR2LINE_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5845261}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_ADDR2LINE_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5845261}
{"args":["APPEND","_CMAKE_ADDR2LINE_FIND_NAMES","addr2line","addr2line","addr2line","addr2line"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5845261}
{"args":["REMOVE_DUPLICATES","_CMAKE_ADDR2LINE_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5845261}
{"args":["CMAKE_ADDR2LINE","NAMES","addr2line","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5845261}
{"args":["_CMAKE_ADDR2LINE_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5855198}
{"args":["_CMAKE_TAPI_FIND_NAMES",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5855198}
{"args":["_CMAKE_TOOL_NAME","IN","LISTS","_CMAKE_TAPI_NAMES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":213,"time":1751960211.5855198}
{"args":["APPEND","_CMAKE_TAPI_FIND_NAMES","tapi","tapi","tapi","tapi"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":214,"line_end":219,"time":1751960211.5855198}
{"args":["REMOVE_DUPLICATES","_CMAKE_TAPI_FIND_NAMES"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":221,"time":1751960211.5855198}
{"args":["CMAKE_TAPI","NAMES","tapi","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":223,"time":1751960211.5855198}
{"args":["_CMAKE_TAPI_FIND_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":224,"time":1751960211.5861263}
{"args":["NOT","CMAKE_RANLIB"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":227,"time":1751960211.5861263}
{"args":["APPLE","AND","TAPI","IN_LIST","_CMAKE_TOOL_VARS","AND","NOT","CMAKE_TAPI"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":231,"time":1751960211.5861263}
{"args":["CMAKE_PLATFORM_HAS_INSTALLNAME"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":246,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL","IN","LISTS","_CMAKE_TOOL_VARS"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":257,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_AR","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5861263}
{"args":["_CMAKE_AR_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_RANLIB","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5861263}
{"args":["_CMAKE_RANLIB_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_STRIP","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5861263}
{"args":["CMAKE_STRIP"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1751960211.5861263}
{"args":["_CMAKE_STRIP_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_LINKER","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5861263}
{"args":["_CMAKE_LINKER_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_NM","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5861263}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5861263}
{"args":["CMAKE_NM"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1751960211.5861263}
{"args":["_CMAKE_NM_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_OBJDUMP","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5871277}
{"args":["CMAKE_OBJDUMP"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1751960211.5871277}
{"args":["_CMAKE_OBJDUMP_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_OBJCOPY","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5871277}
{"args":["CMAKE_OBJCOPY"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1751960211.5871277}
{"args":["_CMAKE_OBJCOPY_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_READELF","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5871277}
{"args":["CMAKE_READELF"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1751960211.5871277}
{"args":["_CMAKE_READELF_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_DLLTOOL","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5871277}
{"args":["CMAKE_DLLTOOL"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1751960211.5871277}
{"args":["_CMAKE_DLLTOOL_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_ADDR2LINE","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5871277}
{"args":["CMAKE_ADDR2LINE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1751960211.5871277}
{"args":["_CMAKE_ADDR2LINE_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED","CACHE","CMAKE_TAPI","PROPERTY","TYPE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":258,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":259,"time":1751960211.5871277}
{"args":["CMAKE_TAPI"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":260,"time":1751960211.5871277}
{"args":["_CMAKE_TAPI_NAMES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":262,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_VARS"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":264,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_CACHED"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":265,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL_NAME"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":266,"time":1751960211.5871277}
{"args":["_CMAKE_TOOL"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":267,"time":1751960211.5871277}
{"args":["xGNU","MATCHES","^xIAR$"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake","frame":3,"global_frame":3,"line":269,"time":1751960211.5871277}
{"args":["Compiler/GNU-FindBinUtils","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":202,"time":1751960211.5871277}
{"args":["NOT","DEFINED","_CMAKE_PROCESSING_LANGUAGE","OR","_CMAKE_PROCESSING_LANGUAGE","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake","frame":3,"global_frame":3,"line":1,"time":1751960211.5871277}
{"args":["REGEX","MATCH","^([0-9]+)","__version_x","15.1.0"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake","frame":3,"global_frame":3,"line":8,"line_end":9,"time":1751960211.5871277}
{"args":["REGEX","MATCH","^([0-9]+\\.[0-9]+)","__version_x_y","15.1.0"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake","frame":3,"global_frame":3,"line":11,"line_end":12,"time":1751960211.5871277}
{"args":["__gcc_hints","C:/msys64/mingw64/bin/cc.EXE","DIRECTORY"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake","frame":3,"global_frame":3,"line":15,"time":1751960211.5871277}
{"args":["CMAKE_C_COMPILER_AR","NAMES","gcc-ar-15.1","gcc-ar-15","gcc-ar15","gcc-ar","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH","DOC","A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake","frame":3,"global_frame":3,"line":18,"line_end":26,"time":1751960211.5871277}
{"args":["CMAKE_C_COMPILER_AR"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake","frame":3,"global_frame":3,"line":27,"time":1751960211.5871277}
{"args":["CMAKE_C_COMPILER_RANLIB","NAMES","gcc-ranlib-15.1","gcc-ranlib-15","gcc-ranlib15","gcc-ranlib","HINTS","C:/msys64/mingw64/bin","NO_CMAKE_PATH","NO_CMAKE_ENVIRONMENT_PATH","DOC","A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake","frame":3,"global_frame":3,"line":30,"line_end":38,"time":1751960211.5871277}
{"args":["CMAKE_C_COMPILER_RANLIB"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake","frame":3,"global_frame":3,"line":39,"time":1751960211.5871277}
{"args":["_CMAKE_PROCESSING_LANGUAGE"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":203,"time":1751960211.5871277}
{"args":["CMAKE_C_COMPILER_SYSROOT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":205,"time":1751960211.5871277}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":209,"time":1751960211.5871277}
{"args":["_SET_CMAKE_C_COMPILER_SYSROOT",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":210,"time":1751960211.5871277}
{"args":["CMAKE_C_COMPILER_ARCHITECTURE_ID"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":213,"time":1751960211.5871277}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":216,"time":1751960211.5871277}
{"args":["_SET_CMAKE_C_COMPILER_ARCHITECTURE_ID",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":217,"time":1751960211.5871277}
{"args":["MSVC_C_ARCHITECTURE_ID"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":220,"time":1751960211.5871277}
{"args":["CMAKE_C_XCODE_ARCHS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":225,"time":1751960211.5871277}
{"args":["C:/msys64/mingw64/share/cmake/Modules/CMakeCCompiler.cmake.in","C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","@ONLY"],"cmd":"configure_file","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":231,"line_end":234,"time":1751960211.5871277}
{"args":["CMAKE_C_COMPILER_ENV_VAR","CC"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake","frame":2,"global_frame":2,"line":235,"time":1751960211.5891342}
{"args":["CMAKE_C_COMPILER","C:/msys64/mingw64/bin/cc.EXE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":1,"time":1751960211.5901337}
{"args":["CMAKE_C_COMPILER_ARG1",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":2,"time":1751960211.5901337}
{"args":["CMAKE_C_COMPILER_ID","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":3,"time":1751960211.5901337}
{"args":["CMAKE_C_COMPILER_VERSION","15.1.0"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":4,"time":1751960211.5901337}
{"args":["CMAKE_C_COMPILER_VERSION_INTERNAL",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":5,"time":1751960211.5901337}
{"args":["CMAKE_C_COMPILER_WRAPPER",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":6,"time":1751960211.5901337}
{"args":["CMAKE_C_STANDARD_COMPUTED_DEFAULT","23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":7,"time":1751960211.5901337}
{"args":["CMAKE_C_EXTENSIONS_COMPUTED_DEFAULT","ON"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":8,"time":1751960211.5901337}
{"args":["CMAKE_C_STANDARD_LATEST","23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":9,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILE_FEATURES","c_std_90;c_function_prototypes;c_std_99;c_restrict;c_variadic_macros;c_std_11;c_static_assert;c_std_17;c_std_23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":10,"time":1751960211.5911331}
{"args":["CMAKE_C90_COMPILE_FEATURES","c_std_90;c_function_prototypes"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":11,"time":1751960211.5911331}
{"args":["CMAKE_C99_COMPILE_FEATURES","c_std_99;c_restrict;c_variadic_macros"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":12,"time":1751960211.5911331}
{"args":["CMAKE_C11_COMPILE_FEATURES","c_std_11;c_static_assert"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":13,"time":1751960211.5911331}
{"args":["CMAKE_C17_COMPILE_FEATURES","c_std_17"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":14,"time":1751960211.5911331}
{"args":["CMAKE_C23_COMPILE_FEATURES","c_std_23"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":15,"time":1751960211.5911331}
{"args":["CMAKE_C_PLATFORM_ID","MinGW"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":17,"time":1751960211.5911331}
{"args":["CMAKE_C_SIMULATE_ID",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":18,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":19,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_APPLE_SYSROOT",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":20,"time":1751960211.5911331}
{"args":["CMAKE_C_SIMULATE_VERSION",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":21,"time":1751960211.5911331}
{"args":["CMAKE_AR","C:/msys64/mingw64/bin/ar.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":26,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_AR","C:/msys64/mingw64/bin/gcc-ar.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":27,"time":1751960211.5911331}
{"args":["CMAKE_RANLIB","C:/msys64/mingw64/bin/ranlib.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":28,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_RANLIB","C:/msys64/mingw64/bin/gcc-ranlib.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":29,"time":1751960211.5911331}
{"args":["CMAKE_LINKER","C:/msys64/mingw64/bin/ld.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":30,"time":1751960211.5911331}
{"args":["CMAKE_LINKER_LINK",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":31,"time":1751960211.5911331}
{"args":["CMAKE_LINKER_LLD",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":32,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_LINKER","C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":33,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_LINKER_ID","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":34,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_LINKER_VERSION","2.44"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":35,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_LINKER_FRONTEND_VARIANT","GNU"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":36,"time":1751960211.5911331}
{"args":["CMAKE_MT",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":37,"time":1751960211.5911331}
{"args":["CMAKE_TAPI","CMAKE_TAPI-NOTFOUND"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":38,"time":1751960211.5911331}
{"args":["CMAKE_COMPILER_IS_GNUCC","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":39,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_LOADED","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":40,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_WORKS","TRUE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":41,"time":1751960211.5911331}
{"args":["CMAKE_C_ABI_COMPILED","TRUE"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":42,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_ENV_VAR","CC"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":44,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_ID_RUN","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":46,"time":1751960211.5911331}
{"args":["CMAKE_C_SOURCE_FILE_EXTENSIONS","c;m"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":47,"time":1751960211.5911331}
{"args":["CMAKE_C_IGNORE_EXTENSIONS","h;H;o;O;obj;OBJ;def;DEF;rc;RC"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":48,"time":1751960211.5911331}
{"args":["CMAKE_C_LINKER_PREFERENCE","10"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":49,"time":1751960211.5911331}
{"args":["CMAKE_C_LINKER_DEPFILE_SUPPORTED"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":50,"time":1751960211.5911331}
{"args":["CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":51,"time":1751960211.5911331}
{"args":["CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":52,"time":1751960211.5911331}
{"args":["CMAKE_C_SIZEOF_DATA_PTR","8"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":55,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_ABI",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":56,"time":1751960211.5911331}
{"args":["CMAKE_C_BYTE_ORDER","LITTLE_ENDIAN"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":57,"time":1751960211.5911331}
{"args":["CMAKE_C_LIBRARY_ARCHITECTURE",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":58,"time":1751960211.5911331}
{"args":["CMAKE_C_SIZEOF_DATA_PTR"],"cmd":"if","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":60,"time":1751960211.5911331}
{"args":["CMAKE_SIZEOF_VOID_P","8"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":61,"time":1751960211.5911331}
{"args":["CMAKE_C_COMPILER_ABI"],"cmd":"if","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":64,"time":1751960211.5911331}
{"args":["CMAKE_C_LIBRARY_ARCHITECTURE"],"cmd":"if","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":68,"time":1751960211.5911331}
{"args":["CMAKE_C_CL_SHOWINCLUDES_PREFIX",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":72,"time":1751960211.5911331}
{"args":["CMAKE_C_CL_SHOWINCLUDES_PREFIX"],"cmd":"if","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":73,"time":1751960211.5911331}
{"args":["CMAKE_C_IMPLICIT_INCLUDE_DIRECTORIES","C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;C:/msys64/mingw64/include;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":81,"time":1751960211.5911331}
{"args":["CMAKE_C_IMPLICIT_LINK_LIBRARIES","mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":82,"time":1751960211.5911331}
{"args":["CMAKE_C_IMPLICIT_LINK_DIRECTORIES","C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/msys64/mingw64/lib/gcc;C:/msys64/mingw64/x86_64-w64-mingw32/lib;C:/msys64/mingw64/lib"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":83,"time":1751960211.5911331}
{"args":["CMAKE_C_IMPLICIT_LINK_FRAMEWORK_DIRECTORIES",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","frame":2,"global_frame":2,"line":84,"time":1751960211.5911331}
{"args":["_cmake_record_install_prefix"],"cmd":"function","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":9,"time":1751960211.5911331}
{"args":["CMakeGenericSystem"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":27,"time":1751960211.5911331}
{"args":["CMakeInitializeConfigs"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":4,"time":1751960211.5911331}
{"args":["GLOBAL"],"cmd":"include_guard","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":4,"time":1751960211.5921345}
{"args":["cmake_initialize_per_config_variable","_PREFIX","_DOCSTRING"],"cmd":"function","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":8,"time":1751960211.5921345}
{"args":["CMAKE_SHARED_LIBRARY_C_FLAGS",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":6,"time":1751960211.5921345}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS","-shared"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":7,"time":1751960211.5921345}
{"args":["CMAKE_SHARED_LIBRARY_LINK_C_FLAGS",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":8,"time":1751960211.5921345}
{"args":["CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":9,"time":1751960211.5921345}
{"args":["CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG_SEP",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":10,"time":1751960211.5921345}
{"args":["CMAKE_INCLUDE_FLAG_C","-I"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":11,"time":1751960211.5921345}
{"args":["CMAKE_LIBRARY_PATH_FLAG","-L"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":12,"time":1751960211.5921345}
{"args":["CMAKE_LIBRARY_PATH_TERMINATOR",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":13,"time":1751960211.5921345}
{"args":["CMAKE_LINK_LIBRARY_FLAG","-l"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":14,"time":1751960211.5921345}
{"args":["CMAKE_LINK_LIBRARY_SUFFIX",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":16,"time":1751960211.5921345}
{"args":["CMAKE_STATIC_LIBRARY_PREFIX","lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":17,"time":1751960211.5921345}
{"args":["CMAKE_STATIC_LIBRARY_SUFFIX",".a"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":18,"time":1751960211.5921345}
{"args":["CMAKE_SHARED_LIBRARY_PREFIX","lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":19,"time":1751960211.5921345}
{"args":["CMAKE_SHARED_LIBRARY_SUFFIX",".so"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":20,"time":1751960211.5921345}
{"args":["CMAKE_EXECUTABLE_SUFFIX",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":21,"time":1751960211.5921345}
{"args":["CMAKE_DL_LIBS","dl"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":22,"time":1751960211.5921345}
{"args":["CMAKE_FIND_LIBRARY_PREFIXES","lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":24,"time":1751960211.5921345}
{"args":["CMAKE_FIND_LIBRARY_SUFFIXES",".so",".a"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":25,"time":1751960211.5921345}
{"args":["CMAKE_LINK_LIBRARY_USING_DEFAULT_SUPPORTED","TRUE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":31,"time":1751960211.5921345}
{"args":["NOT","DEFINED","CMAKE_AUTOGEN_ORIGIN_DEPENDS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":33,"time":1751960211.5921345}
{"args":["CMAKE_AUTOGEN_ORIGIN_DEPENDS","ON"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":34,"time":1751960211.5921345}
{"args":["NOT","DEFINED","CMAKE_AUTOMOC_COMPILER_PREDEFINES"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":36,"time":1751960211.5921345}
{"args":["CMAKE_AUTOMOC_COMPILER_PREDEFINES","ON"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":37,"time":1751960211.5921345}
{"args":["NOT","DEFINED","CMAKE_AUTOMOC_PATH_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":39,"time":1751960211.5921345}
{"args":["CMAKE_AUTOMOC_PATH_PREFIX","OFF"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":40,"time":1751960211.5921345}
{"args":["NOT","DEFINED","CMAKE_AUTOMOC_MACRO_NAMES"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":42,"time":1751960211.5921345}
{"args":["CMAKE_AUTOMOC_MACRO_NAMES","Q_OBJECT","Q_GADGET","Q_NAMESPACE","Q_NAMESPACE_EXPORT"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":43,"time":1751960211.5921345}
{"args":["GLOBAL","PROPERTY","TARGET_SUPPORTS_SHARED_LIBS","TRUE"],"cmd":"set_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":47,"time":1751960211.5921345}
{"args":["CMAKE_SKIP_RPATH","NO","CACHE","BOOL","If set, runtime paths are not added when using shared libraries."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":49,"line_end":50,"time":1751960211.5921345}
{"args":["CMAKE_SKIP_INSTALL_RPATH","NO","CACHE","BOOL","If set, runtime paths are not added when installing shared libraries, but are added when building."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":51,"line_end":52,"time":1751960211.5921345}
{"args":["CMAKE_VERBOSE_MAKEFILE","FALSE","CACHE","BOOL","If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":54,"time":1751960211.5921345}
{"args":["DEFINED","ENV{CMAKE_COLOR_DIAGNOSTICS}","AND","NOT","DEFINED","CACHE{CMAKE_COLOR_DIAGNOSTICS}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":56,"time":1751960211.5921345}
{"args":["CMAKE_GENERATOR","MATCHES","Make"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":60,"time":1751960211.5921345}
{"args":["NOT","DEFINED","CMAKE_EXPORT_COMPILE_COMMANDS","AND","CMAKE_GENERATOR","MATCHES","Ninja|Unix Makefiles"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":74,"time":1751960211.5921345}
{"args":["CMAKE_EXPORT_COMPILE_COMMANDS","","CACHE","BOOL","Enable/Disable output of compile commands during generation."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":75,"line_end":77,"time":1751960211.5921345}
{"args":["CMAKE_EXPORT_COMPILE_COMMANDS"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":78,"time":1751960211.5921345}
{"args":["NOT","DEFINED","CMAKE_EXPORT_BUILD_DATABASE","AND","CMAKE_GENERATOR","MATCHES","Ninja"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":81,"time":1751960211.5921345}
{"args":["CMAKE_EXPORT_BUILD_DATABASE","","CACHE","BOOL","Enable/Disable output of build database during the build."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":82,"line_end":84,"time":1751960211.5921345}
{"args":["CMAKE_EXPORT_BUILD_DATABASE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":85,"time":1751960211.5921345}
{"args":["GetDefaultWindowsPrefixBase","var"],"cmd":"function","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":95,"time":1751960211.5921345}
{"args":["NOT","DEFINED","CMAKE_INSTALL_PREFIX","AND","NOT","DEFINED","ENV{CMAKE_INSTALL_PREFIX}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":187,"line_end":188,"time":1751960211.5921345}
{"args":["CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":189,"time":1751960211.5921345}
{"args":["DEFINED","ENV{CMAKE_INSTALL_PREFIX}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":192,"time":1751960211.5921345}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":195,"time":1751960211.5921345}
{"args":["CMAKE_HOST_UNIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":198,"time":1751960211.5921345}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":201,"time":1751960211.5921345}
{"args":["CMAKE_GENERIC_PROGRAM_FILES"],"cmd":"GetDefaultWindowsPrefixBase","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":202,"time":1751960211.5921345}
{"args":["Ninja","MATCHES","(Win64|IA64)"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":102,"time":1751960211.5921345}
{"args":["","MATCHES","x64"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":104,"time":1751960211.5921345}
{"args":["","MATCHES","ARM64"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":106,"time":1751960211.5921345}
{"args":["Ninja","MATCHES","ARM"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":108,"time":1751960211.5921345}
{"args":["","MATCHES","ARM"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":110,"time":1751960211.5921345}
{"args":["8","STREQUAL","8"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":112,"time":1751960211.5921345}
{"args":["arch_hint","x64"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":113,"time":1751960211.5921345}
{"args":["NOT","arch_hint"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":118,"time":1751960211.5921345}
{"args":["_PREFIX_ENV_VAR","ProgramFiles"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":140,"time":1751960211.5921345}
{"args":["C:\\Program Files","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":142,"time":1751960211.5921345}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":146,"time":1751960211.5921345}
{"args":["C:\\Program Files","STREQUAL","C:\\Program Files"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":148,"time":1751960211.5921345}
{"args":["NOT","x64","STREQUAL","x64"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":151,"time":1751960211.5921345}
{"args":["NOT","C:\\Program Files","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":171,"time":1751960211.5921345}
{"args":["TO_CMAKE_PATH","C:\\Program Files","_base"],"cmd":"file","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":172,"time":1751960211.5921345}
{"args":["CMAKE_GENERIC_PROGRAM_FILES","C:/Program Files","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":4,"global_frame":4,"line":179,"time":1751960211.5921345}
{"args":["CMAKE_INSTALL_PREFIX","C:/Program Files/MesonTemp","CACHE","PATH","Install path prefix, prepended onto install directories."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":203,"line_end":205,"time":1751960211.5921345}
{"args":["CMAKE_GENERIC_PROGRAM_FILES"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":206,"time":1751960211.5921345}
{"args":["CMAKE_INSTALL_DEFAULT_COMPONENT_NAME","Unspecified"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":212,"time":1751960211.5921345}
{"args":["CMAKE_SKIP_RPATH","CMAKE_SKIP_INSTALL_RPATH","CMAKE_VERBOSE_MAKEFILE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake","frame":3,"global_frame":3,"line":214,"line_end":218,"time":1751960211.5921345}
{"args":["CMAKE_SYSTEM_INFO_FILE","Platform/Windows"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":30,"time":1751960211.5921345}
{"args":["Platform/Windows","OPTIONAL","RESULT_VARIABLE","_INCLUDED_SYSTEM_INFO_FILE"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":32,"time":1751960211.5931339}
{"args":["CMAKE_STATIC_LIBRARY_PREFIX",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":1,"time":1751960211.5931339}
{"args":["CMAKE_STATIC_LIBRARY_SUFFIX",".lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":2,"time":1751960211.5931339}
{"args":["CMAKE_SHARED_LIBRARY_PREFIX",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":3,"time":1751960211.5931339}
{"args":["CMAKE_SYSTEM_NAME","STREQUAL","WindowsKernelModeDriver"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":4,"time":1751960211.5931339}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":6,"time":1751960211.5931339}
{"args":["CMAKE_SHARED_LIBRARY_SUFFIX",".dll"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":7,"time":1751960211.5931339}
{"args":["CMAKE_IMPORT_LIBRARY_PREFIX",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":9,"time":1751960211.5931339}
{"args":["CMAKE_IMPORT_LIBRARY_SUFFIX",".lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":10,"time":1751960211.5931339}
{"args":["CMAKE_EXECUTABLE_SUFFIX",".exe"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":11,"time":1751960211.5931339}
{"args":["CMAKE_LINK_LIBRARY_SUFFIX",".lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":12,"time":1751960211.5931339}
{"args":["CMAKE_DL_LIBS",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":13,"time":1751960211.5931339}
{"args":["CMAKE_EXTRA_LINK_EXTENSIONS",".targets"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":14,"time":1751960211.5931339}
{"args":["CMAKE_FIND_LIBRARY_PREFIXES","","lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":16,"line_end":19,"time":1751960211.5931339}
{"args":["CMAKE_FIND_LIBRARY_SUFFIXES",".dll.lib",".lib",".a"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":20,"line_end":24,"time":1751960211.5931339}
{"args":["CMAKE_GENERATOR","MATCHES","Borland"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":28,"time":1751960211.5931339}
{"args":["CMAKE_GENERATOR","MATCHES","NMake"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":35,"time":1751960211.5931339}
{"args":["Platform/WindowsPaths"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake","frame":3,"global_frame":3,"line":40,"time":1751960211.5931339}
{"args":["__WINDOWS_PATHS_INCLUDED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":10,"time":1751960211.5931339}
{"args":["__WINDOWS_PATHS_INCLUDED","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":13,"time":1751960211.5931339}
{"args":["_programfiles",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.5931339}
{"args":["v","ProgramW6432","ProgramFiles","ProgramFiles(x86)"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.5931339}
{"args":["DEFINED","ENV{ProgramW6432}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":33,"time":1751960211.5931339}
{"args":["TO_CMAKE_PATH","C:\\Program Files","_env_programfiles"],"cmd":"file","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":34,"time":1751960211.5931339}
{"args":["APPEND","_programfiles","C:/Program Files"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.5931339}
{"args":["_env_programfiles"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":36,"time":1751960211.5931339}
{"args":["DEFINED","ENV{ProgramFiles}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":33,"time":1751960211.5931339}
{"args":["TO_CMAKE_PATH","C:\\Program Files","_env_programfiles"],"cmd":"file","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":34,"time":1751960211.5931339}
{"args":["APPEND","_programfiles","C:/Program Files"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.5931339}
{"args":["_env_programfiles"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":36,"time":1751960211.5931339}
{"args":["DEFINED","ENV{ProgramFiles(x86)}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":33,"time":1751960211.5931339}
{"args":["TO_CMAKE_PATH","C:\\Program Files (x86)","_env_programfiles"],"cmd":"file","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":34,"time":1751960211.5931339}
{"args":["APPEND","_programfiles","C:/Program Files (x86)"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.5931339}
{"args":["_env_programfiles"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":36,"time":1751960211.5931339}
{"args":["DEFINED","ENV{SystemDrive}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":39,"time":1751960211.5931339}
{"args":["d","Program Files","Program Files (x86)"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":40,"time":1751960211.5931339}
{"args":["EXISTS","C:/Program Files"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":41,"time":1751960211.5931339}
{"args":["APPEND","_programfiles","C:/Program Files"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":42,"time":1751960211.5931339}
{"args":["EXISTS","C:/Program Files (x86)"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":41,"time":1751960211.5931339}
{"args":["APPEND","_programfiles","C:/Program Files (x86)"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":42,"time":1751960211.5931339}
{"args":["_programfiles"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":46,"time":1751960211.5931339}
{"args":["REMOVE_DUPLICATES","_programfiles"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":47,"time":1751960211.5931339}
{"args":["APPEND","CMAKE_SYSTEM_PREFIX_PATH","C:/Program Files;C:/Program Files (x86)"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":48,"time":1751960211.5931339}
{"args":["_programfiles"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":50,"time":1751960211.5931339}
{"args":["_CMAKE_INSTALL_DIR","C:/msys64/mingw64/share/cmake","PATH"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":53,"time":1751960211.5931339}
{"args":["_CMAKE_INSTALL_DIR","C:/msys64/mingw64/share","PATH"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":54,"time":1751960211.5931339}
{"args":["APPEND","CMAKE_SYSTEM_PREFIX_PATH","C:/msys64/mingw64"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":55,"time":1751960211.5931339}
{"args":["NOT","CMAKE_FIND_NO_INSTALL_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":57,"time":1751960211.5931339}
{"args":["APPEND","CMAKE_SYSTEM_PREFIX_PATH","C:/Program Files/MesonTemp"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":59,"line_end":62,"time":1751960211.5931339}
{"args":["CMAKE_STAGING_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":63,"time":1751960211.5941334}
{"args":[],"cmd":"_cmake_record_install_prefix","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":70,"time":1751960211.5941334}
{"args":["_CMAKE_SYSTEM_PREFIX_PATH_INSTALL_PREFIX_VALUE","C:/Program Files/MesonTemp","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":10,"time":1751960211.5941334}
{"args":["_CMAKE_SYSTEM_PREFIX_PATH_STAGING_PREFIX_VALUE","","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":11,"time":1751960211.5941334}
{"args":["icount","0"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":12,"time":1751960211.5941334}
{"args":["scount","0"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":13,"time":1751960211.5941334}
{"args":["value","IN","LISTS","CMAKE_SYSTEM_PREFIX_PATH"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":14,"time":1751960211.5941334}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1751960211.5941334}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1751960211.5941334}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1751960211.5941334}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1751960211.5941334}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1751960211.5941334}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1751960211.5941334}
{"args":["value","STREQUAL","CMAKE_INSTALL_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":15,"time":1751960211.5941334}
{"args":["EXPR","icount","0+1"],"cmd":"math","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":16,"time":1751960211.5941334}
{"args":["value","STREQUAL","CMAKE_STAGING_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1751960211.5941334}
{"args":["_CMAKE_SYSTEM_PREFIX_PATH_INSTALL_PREFIX_COUNT","1","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":22,"time":1751960211.5941334}
{"args":["_CMAKE_SYSTEM_PREFIX_PATH_STAGING_PREFIX_COUNT","0","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":5,"global_frame":5,"line":23,"time":1751960211.5941334}
{"args":["CMAKE_CROSSCOMPILING","AND","NOT","CMAKE_HOST_SYSTEM_NAME","MATCHES","Windows"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":72,"time":1751960211.5941334}
{"args":["APPEND","CMAKE_SYSTEM_INCLUDE_PATH"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":77,"line_end":78,"time":1751960211.5941334}
{"args":["NOT","CMAKE_FIND_NO_INSTALL_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":81,"time":1751960211.5941334}
{"args":["APPEND","CMAKE_SYSTEM_LIBRARY_PATH","C:/Program Files/MesonTemp/bin"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":82,"line_end":84,"time":1751960211.5941334}
{"args":["CMAKE_STAGING_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":85,"time":1751960211.5941334}
{"args":["APPEND","CMAKE_SYSTEM_LIBRARY_PATH","C:/msys64/mingw64/bin","/bin"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":91,"line_end":94,"time":1751960211.5941334}
{"args":["APPEND","CMAKE_SYSTEM_PROGRAM_PATH"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake","frame":4,"global_frame":4,"line":96,"line_end":97,"time":1751960211.5941334}
{"args":["NOT","_INCLUDED_SYSTEM_INFO_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":34,"time":1751960211.5941334}
{"args":["CMAKE_EXTRA_GENERATOR"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":48,"time":1751960211.5941334}
{"args":["NOT","CMAKE_MODULE_EXISTS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":58,"time":1751960211.5941334}
{"args":["CMAKE_SHARED_MODULE_PREFIX",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":59,"time":1751960211.5941334}
{"args":["CMAKE_SHARED_MODULE_SUFFIX",".dll"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":60,"time":1751960211.5941334}
{"args":["CMAKE_SYSTEM_SPECIFIC_INFORMATION_LOADED","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake","frame":2,"global_frame":2,"line":64,"time":1751960211.5941334}
{"args":["CMakeLanguageInformation"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":11,"time":1751960211.5946388}
{"args":["__cmake_include_compiler_wrapper","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake","frame":3,"global_frame":3,"line":9,"time":1751960211.5946388}
{"args":["UNIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":15,"time":1751960211.5946388}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":17,"time":1751960211.5946388}
{"args":["CMAKE_C_OUTPUT_EXTENSION",".obj"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":18,"time":1751960211.5946388}
{"args":["_INCLUDED_FILE","0"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":21,"time":1751960211.5946388}
{"args":["CMAKE_C_COMPILER_ID"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":24,"time":1751960211.5946388}
{"args":["Compiler/GNU-C","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":25,"time":1751960211.5946388}
{"args":["Compiler/GNU"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake","frame":3,"global_frame":3,"line":1,"time":1751960211.5946388}
{"args":["__COMPILER_GNU"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":6,"time":1751960211.5946388}
{"args":["__COMPILER_GNU","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":9,"time":1751960211.5946388}
{"args":["Compiler/CMakeCommonCompilerMacros"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":11,"time":1751960211.5946388}
{"args":["__COMPILER_CMAKE_COMMON_COMPILER_MACROS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":5,"time":1751960211.595644}
{"args":["__COMPILER_CMAKE_COMMON_COMPILER_MACROS","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":8,"time":1751960211.595644}
{"args":["__compiler_check_default_language_standard","lang","stdver1","std1"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":32,"time":1751960211.595644}
{"args":["cmake_record_c_compile_features"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":71,"time":1751960211.595644}
{"args":["cmake_record_cxx_compile_features"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":106,"time":1751960211.595644}
{"args":["cmake_record_cuda_compile_features"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":146,"time":1751960211.595644}
{"args":["cmake_record_hip_compile_features"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":186,"time":1751960211.595644}
{"args":["cmake_create_cxx_import_std","std","variable"],"cmd":"function","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":5,"global_frame":5,"line":205,"time":1751960211.595644}
{"args":["__pch_header_C","c-header"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":13,"time":1751960211.595644}
{"args":["__pch_header_CXX","c++-header"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":14,"time":1751960211.595644}
{"args":["__pch_header_OBJC","objective-c-header"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":15,"time":1751960211.595644}
{"args":["__pch_header_OBJCXX","objective-c++-header"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":16,"time":1751960211.595644}
{"args":["__compiler_gnu","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":18,"time":1751960211.595644}
{"args":["__compiler_gnu_c_standards","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":159,"time":1751960211.595644}
{"args":["__compiler_gnu_cxx_standards","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":202,"time":1751960211.595644}
{"args":["C"],"cmd":"__compiler_gnu","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake","frame":3,"global_frame":3,"line":2,"time":1751960211.595644}
{"args":["CMAKE_C_VERBOSE_FLAG","-v"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":20,"time":1751960211.595644}
{"args":["CMAKE_C_COMPILE_OPTIONS_WARNING_AS_ERROR","-Werror"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":21,"time":1751960211.595644}
{"args":["CMAKE_C_COMPILE_OPTIONS_PIC","-fPIC"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":22,"time":1751960211.595644}
{"args":["_CMAKE_C_PIE_MAY_BE_SUPPORTED_BY_LINKER","NO"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":23,"time":1751960211.595644}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","3.4"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":24,"time":1751960211.595644}
{"args":["CMAKE_C_COMPILE_OPTIONS_PIE","-fPIE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":25,"time":1751960211.595644}
{"args":["_CMAKE_C_PIE_MAY_BE_SUPPORTED_BY_LINKER","YES"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":28,"time":1751960211.595644}
{"args":["CMAKE_C_LINK_OPTIONS_PIE","-fPIE","-pie"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":29,"time":1751960211.595644}
{"args":["CMAKE_C_LINK_OPTIONS_NO_PIE","-no-pie"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.595644}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.0"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.595644}
{"args":["CMAKE_C_COMPILE_OPTIONS_VISIBILITY","-fvisibility="],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":33,"time":1751960211.595644}
{"args":["CMAKE_SHARED_LIBRARY_C_FLAGS","-fPIC"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.595644}
{"args":["CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS","-shared"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":36,"time":1751960211.595644}
{"args":["CMAKE_C_COMPILE_OPTIONS_SYSROOT","--sysroot="],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":37,"time":1751960211.595644}
{"args":["CMAKE_C_LINKER_WRAPPER_FLAG","-Wl,"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":39,"time":1751960211.595644}
{"args":["CMAKE_C_LINKER_WRAPPER_FLAG_SEP",","],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":40,"time":1751960211.595644}
{"args":["CMAKE_C_LINK_MODE","DRIVER"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":42,"time":1751960211.595644}
{"args":["_IN_TC","GLOBAL","PROPERTY","IN_TRY_COMPILE"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":48,"time":1751960211.595644}
{"args":["CMAKE_C_COMPILER_ID","STREQUAL","GNU","AND","_IN_TC","AND","NOT","CMAKE_FORCE_DEPFILES"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":49,"time":1751960211.595644}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":50,"time":1751960211.595644}
{"args":["CMAKE_DEPFILE_FLAGS_C","-MD -MT <DEP_TARGET> -MF <DEP_FILE>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":54,"time":1751960211.595644}
{"args":["APPEND","CMAKE_C_FLAGS_INIT"," "],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":58,"time":1751960211.595644}
{"args":["APPEND","CMAKE_C_FLAGS_DEBUG_INIT"," -g"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":59,"time":1751960211.595644}
{"args":["APPEND","CMAKE_C_FLAGS_MINSIZEREL_INIT"," -Os"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":60,"time":1751960211.595644}
{"args":["APPEND","CMAKE_C_FLAGS_RELEASE_INIT"," -O3"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":61,"time":1751960211.595644}
{"args":["APPEND","CMAKE_C_FLAGS_RELWITHDEBINFO_INIT"," -O2 -g"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":62,"time":1751960211.595644}
{"args":["NOT","xC","STREQUAL","xFortran"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":63,"time":1751960211.595644}
{"args":["APPEND","CMAKE_C_FLAGS_MINSIZEREL_INIT"," -DNDEBUG"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":64,"time":1751960211.595644}
{"args":["APPEND","CMAKE_C_FLAGS_RELEASE_INIT"," -DNDEBUG"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":65,"time":1751960211.595644}
{"args":["APPEND","CMAKE_C_FLAGS_RELWITHDEBINFO_INIT"," -DNDEBUG"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":66,"time":1751960211.595644}
{"args":["CMAKE_C_CREATE_PREPROCESSED_SOURCE","<CMAKE_C_COMPILER> <DEFINES> <INCLUDES> <FLAGS> -E <SOURCE> > <PREPROCESSED_SOURCE>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":68,"time":1751960211.595644}
{"args":["CMAKE_C_CREATE_ASSEMBLY_SOURCE","<CMAKE_C_COMPILER> <DEFINES> <INCLUDES> <FLAGS> -S <SOURCE> -o <ASSEMBLY_SOURCE>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":69,"time":1751960211.595644}
{"args":["NOT","APPLE","OR","NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":70,"time":1751960211.595644}
{"args":["CMAKE_INCLUDE_SYSTEM_FLAG_C","-isystem "],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":71,"time":1751960211.595644}
{"args":["_CMAKE_C_IPO_SUPPORTED_BY_CMAKE","YES"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":74,"time":1751960211.595644}
{"args":["_CMAKE_C_IPO_MAY_BE_SUPPORTED_BY_COMPILER","NO"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":75,"time":1751960211.595644}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.5"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":80,"time":1751960211.5966682}
{"args":["_CMAKE_C_IPO_MAY_BE_SUPPORTED_BY_COMPILER","YES"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":81,"time":1751960211.5966682}
{"args":["__lto_flags",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":83,"time":1751960211.5966682}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","11.0"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":90,"time":1751960211.5966682}
{"args":["APPEND","__lto_flags","-flto=auto"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":91,"time":1751960211.5966682}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.7","AND","NOT","APPLE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":102,"time":1751960211.5966682}
{"args":["APPEND","__lto_flags","-fno-fat-lto-objects"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":106,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILE_OPTIONS_IPO","-flto=auto;-fno-fat-lto-objects"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":109,"time":1751960211.5966682}
{"args":["CMAKE_C_ARCHIVE_CREATE_IPO","\"C:/msys64/mingw64/bin/gcc-ar.exe\" qc <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":118,"line_end":120,"time":1751960211.5966682}
{"args":["CMAKE_C_ARCHIVE_APPEND_IPO","\"C:/msys64/mingw64/bin/gcc-ar.exe\" q <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":122,"line_end":124,"time":1751960211.5966682}
{"args":["CMAKE_C_ARCHIVE_FINISH_IPO","\"C:/msys64/mingw64/bin/gcc-ranlib.exe\" <TARGET>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":126,"line_end":128,"time":1751960211.5966682}
{"args":["C","STREQUAL","CXX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":131,"time":1751960211.5966682}
{"args":["NOT","xC","STREQUAL","xFortran"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":141,"time":1751960211.5966682}
{"args":["CMAKE_PCH_EXTENSION",".gch"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":142,"time":1751960211.5966682}
{"args":["NOT","CMAKE_GENERATOR","MATCHES","Xcode"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":143,"time":1751960211.5966682}
{"args":["CMAKE_PCH_PROLOGUE","#pragma GCC system_header"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":144,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILE_OPTIONS_INVALID_PCH","-Winvalid-pch"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":146,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILE_OPTIONS_USE_PCH","-include","<PCH_HEADER>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":147,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILE_OPTIONS_CREATE_PCH","-x","c-header","-include","<PCH_HEADER>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":148,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","4.9"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":153,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILE_OPTIONS_COLOR_DIAGNOSTICS","-fdiagnostics-color=always"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":154,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILE_OPTIONS_COLOR_DIAGNOSTICS_OFF","-fno-diagnostics-color"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":155,"time":1751960211.5966682}
{"args":["C"],"cmd":"__compiler_gnu_c_standards","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake","frame":3,"global_frame":3,"line":3,"time":1751960211.5966682}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.5"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":160,"time":1751960211.5966682}
{"args":["CMAKE_C90_STANDARD_COMPILE_OPTION","-std=c90"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":161,"time":1751960211.5966682}
{"args":["CMAKE_C90_EXTENSION_COMPILE_OPTION","-std=gnu90"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":162,"time":1751960211.5966682}
{"args":["CMAKE_C_STANDARD_LATEST","90"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":163,"time":1751960211.5966682}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","3.4"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":170,"time":1751960211.5966682}
{"args":["CMAKE_C90_STANDARD__HAS_FULL_SUPPORT","ON"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":171,"time":1751960211.5966682}
{"args":["CMAKE_C99_STANDARD_COMPILE_OPTION","-std=c99"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":172,"time":1751960211.5966682}
{"args":["CMAKE_C99_EXTENSION_COMPILE_OPTION","-std=gnu99"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":173,"time":1751960211.5966682}
{"args":["CMAKE_C99_STANDARD__HAS_FULL_SUPPORT","ON"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":174,"time":1751960211.5966682}
{"args":["CMAKE_C_STANDARD_LATEST","99"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":175,"time":1751960211.5966682}
{"args":["NOT","CMAKE_C_COMPILER_VERSION","VERSION_LESS","4.7"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":178,"time":1751960211.5966682}
{"args":["CMAKE_C11_STANDARD_COMPILE_OPTION","-std=c11"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":179,"time":1751960211.5966682}
{"args":["CMAKE_C11_EXTENSION_COMPILE_OPTION","-std=gnu11"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":180,"time":1751960211.5966682}
{"args":["CMAKE_C11_STANDARD__HAS_FULL_SUPPORT","ON"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":181,"time":1751960211.5966682}
{"args":["CMAKE_C_STANDARD_LATEST","11"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":182,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","8.1"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":189,"time":1751960211.5966682}
{"args":["CMAKE_C17_STANDARD_COMPILE_OPTION","-std=c17"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":190,"time":1751960211.5966682}
{"args":["CMAKE_C17_EXTENSION_COMPILE_OPTION","-std=gnu17"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":191,"time":1751960211.5966682}
{"args":["CMAKE_C_STANDARD_LATEST","17"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":192,"time":1751960211.5966682}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","9.1"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":195,"time":1751960211.5976505}
{"args":["CMAKE_C23_STANDARD_COMPILE_OPTION","-std=c2x"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":196,"time":1751960211.5976505}
{"args":["CMAKE_C23_EXTENSION_COMPILE_OPTION","-std=gnu2x"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":197,"time":1751960211.5976505}
{"args":["CMAKE_C_STANDARD_LATEST","23"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake","frame":4,"global_frame":4,"line":198,"time":1751960211.5976505}
{"args":["(","NOT","DEFINED","CMAKE_DEPENDS_USE_COMPILER","OR","CMAKE_DEPENDS_USE_COMPILER",")","AND","CMAKE_GENERATOR","MATCHES","Makefiles|WMake","AND","CMAKE_DEPFILE_FLAGS_C"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake","frame":3,"global_frame":3,"line":6,"line_end":8,"time":1751960211.5976505}
{"args":["CMAKE_C_COMPILE_OPTIONS_EXPLICIT_LANGUAGE","-x","c"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake","frame":3,"global_frame":3,"line":14,"time":1751960211.5976505}
{"args":["C","3.4","90","5.0","11","8.1","17"],"cmd":"__compiler_check_default_language_standard","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake","frame":3,"global_frame":3,"line":16,"time":1751960211.5976505}
{"args":["__std_ver_pairs","3.4;90;5.0;11;8.1;17"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":33,"time":1751960211.5976505}
{"args":["REGEX","REPLACE"," *; *"," ","__std_ver_pairs","3.4;90;5.0;11;8.1;17"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":34,"time":1751960211.5976505}
{"args":["REGEX","MATCHALL","[^ ]+ [^ ]+","__std_ver_pairs","3.4 90 5.0 11 8.1 17"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.5976505}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","3.4"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":39,"time":1751960211.5976505}
{"args":["NOT","CMAKE_C_COMPILER_FORCED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":40,"time":1751960211.5976505}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":46,"time":1751960211.5976505}
{"args":["REVERSE","__std_ver_pairs"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":47,"time":1751960211.5976505}
{"args":["__std_ver_pair","IN","LISTS","__std_ver_pairs"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":48,"time":1751960211.5976505}
{"args":["REGEX","MATCH","([^ ]+) (.+)","__std_ver_pair","8.1 17"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":49,"time":1751960211.5976505}
{"args":["__stdver","8.1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":50,"time":1751960211.5976505}
{"args":["__std","17"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":51,"time":1751960211.5976505}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","__stdver"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":53,"time":1751960211.5976505}
{"args":["NOT","DEFINED","CMAKE_C_EXTENSIONS_DEFAULT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":54,"time":1751960211.5976505}
{"args":["CMAKE_C_EXTENSIONS_DEFAULT","ON"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":56,"time":1751960211.5976505}
{"args":["NOT","DEFINED","CMAKE_C_STANDARD_DEFAULT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":58,"time":1751960211.5976505}
{"args":["CMAKE_C_STANDARD_DEFAULT","17"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":59,"time":1751960211.5976505}
{"args":["__std"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":62,"time":1751960211.5976505}
{"args":["__stdver"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":63,"time":1751960211.5976505}
{"args":["REGEX","MATCH","([^ ]+) (.+)","__std_ver_pair","5.0 11"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":49,"time":1751960211.5976505}
{"args":["__stdver","5.0"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":50,"time":1751960211.5976505}
{"args":["__std","11"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":51,"time":1751960211.5976505}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","__stdver"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":53,"time":1751960211.5976505}
{"args":["NOT","DEFINED","CMAKE_C_EXTENSIONS_DEFAULT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":54,"time":1751960211.5976505}
{"args":["NOT","DEFINED","CMAKE_C_STANDARD_DEFAULT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":58,"time":1751960211.5976505}
{"args":["__std"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":62,"time":1751960211.5976505}
{"args":["__stdver"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":63,"time":1751960211.5976505}
{"args":["REGEX","MATCH","([^ ]+) (.+)","__std_ver_pair","3.4 90"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":49,"time":1751960211.5976505}
{"args":["__stdver","3.4"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":50,"time":1751960211.5976505}
{"args":["__std","90"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":51,"time":1751960211.5976505}
{"args":["CMAKE_C_COMPILER_VERSION","VERSION_GREATER_EQUAL","__stdver"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":53,"time":1751960211.5976505}
{"args":["NOT","DEFINED","CMAKE_C_EXTENSIONS_DEFAULT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":54,"time":1751960211.5976505}
{"args":["NOT","DEFINED","CMAKE_C_STANDARD_DEFAULT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":58,"time":1751960211.5976505}
{"args":["__std"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":62,"time":1751960211.5976505}
{"args":["__stdver"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":63,"time":1751960211.5976505}
{"args":["__std_ver_pairs"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake","frame":4,"global_frame":4,"line":67,"time":1751960211.5976505}
{"args":["CMAKE_BASE_NAME"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":28,"time":1751960211.5976505}
{"args":["CMAKE_BASE_NAME","C:/msys64/mingw64/bin/cc.EXE","NAME_WE"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":29,"time":1751960211.5976505}
{"args":["CMAKE_COMPILER_IS_GNUCC"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":30,"time":1751960211.5976505}
{"args":["CMAKE_BASE_NAME","gcc"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":31,"time":1751960211.5976505}
{"args":["CMAKE_SYSTEM_PROCESSOR"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":36,"time":1751960211.5976505}
{"args":["CMAKE_C_COMPILER_ID"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":37,"time":1751960211.5976505}
{"args":["Platform/Windows-GNU-C-AMD64","OPTIONAL","RESULT_VARIABLE","_INCLUDED_FILE"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":38,"time":1751960211.5976505}
{"args":["NOT","_INCLUDED_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":40,"time":1751960211.5976505}
{"args":["Platform/Windows-gcc-AMD64","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":41,"time":1751960211.5976505}
{"args":["CMAKE_C_COMPILER_ID"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":47,"time":1751960211.5976505}
{"args":["Platform/Windows-GNU-C","OPTIONAL","RESULT_VARIABLE","_INCLUDED_FILE"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":48,"line_end":49,"time":1751960211.5976505}
{"args":["Platform/Windows-GNU"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake","frame":3,"global_frame":3,"line":1,"time":1751960211.5986557}
{"args":["__WINDOWS_GNU"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":6,"time":1751960211.5986557}
{"args":["__WINDOWS_GNU","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":9,"time":1751960211.5986557}
{"args":["MINGW","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":11,"time":1751960211.5986557}
{"args":["CMAKE_HOST_WIN32"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":14,"time":1751960211.5986557}
{"args":["COMMAND","cmake_host_system_information"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":16,"time":1751960211.5986557}
{"args":["RESULT","_MSYSTEM_PREFIX","QUERY","MSYSTEM_PREFIX"],"cmd":"cmake_host_system_information","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":17,"time":1751960211.5986557}
{"args":["_MSYSTEM_PREFIX"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":23,"time":1751960211.6017628}
{"args":["PREPEND","CMAKE_SYSTEM_PREFIX_PATH","C:/msys64/mingw64"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":24,"time":1751960211.6017628}
{"args":["IS_DIRECTORY","C:/msys64/mingw64/local"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":25,"time":1751960211.6017628}
{"args":["_MSYSTEM_PREFIX"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":29,"time":1751960211.6017628}
{"args":["CMAKE_IMPORT_LIBRARY_PREFIX","lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6017628}
{"args":["CMAKE_SHARED_LIBRARY_PREFIX","lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":33,"time":1751960211.6017628}
{"args":["CMAKE_SHARED_MODULE_PREFIX","lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":34,"time":1751960211.6017628}
{"args":["CMAKE_STATIC_LIBRARY_PREFIX","lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6017628}
{"args":["CMAKE_EXECUTABLE_SUFFIX",".exe"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":37,"time":1751960211.6017628}
{"args":["CMAKE_IMPORT_LIBRARY_SUFFIX",".dll.a"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":38,"time":1751960211.6017628}
{"args":["CMAKE_SHARED_LIBRARY_SUFFIX",".dll"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":39,"time":1751960211.6017628}
{"args":["CMAKE_SHARED_MODULE_SUFFIX",".dll"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":40,"time":1751960211.6017628}
{"args":["CMAKE_STATIC_LIBRARY_SUFFIX",".a"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":41,"time":1751960211.6017628}
{"args":["CMAKE_EXTRA_LINK_EXTENSIONS",".lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":43,"time":1751960211.6017628}
{"args":["CMAKE_FIND_LIBRARY_PREFIXES","lib",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":45,"time":1751960211.6017628}
{"args":["CMAKE_FIND_LIBRARY_SUFFIXES",".dll.a",".a",".lib"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":46,"time":1751960211.6017628}
{"args":["CMAKE_C_STANDARD_LIBRARIES_INIT","-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":47,"time":1751960211.6017628}
{"args":["CMAKE_CXX_STANDARD_LIBRARIES_INIT","-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":48,"time":1751960211.6017628}
{"args":["CMAKE_DL_LIBS",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":50,"time":1751960211.6017628}
{"args":["CMAKE_LIBRARY_PATH_FLAG","-L"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":51,"time":1751960211.6017628}
{"args":["CMAKE_LINK_LIBRARY_FLAG","-l"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":52,"time":1751960211.6017628}
{"args":["CMAKE_LINK_DEF_FILE_FLAG",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":53,"time":1751960211.6017628}
{"args":["CMAKE_LINK_LIBRARY_SUFFIX",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":54,"time":1751960211.6017628}
{"args":["CMAKE_GNULD_IMAGE_VERSION","-Wl,--major-image-version,<TARGET_VERSION_MAJOR>,--minor-image-version,<TARGET_VERSION_MINOR>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":56,"line_end":57,"time":1751960211.6017628}
{"args":["__WINDOWS_GNU_LD_RESPONSE","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":60,"time":1751960211.6017628}
{"args":["COMMAND","ld","-v","OUTPUT_VARIABLE","_help","ERROR_VARIABLE","_help"],"cmd":"execute_process","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":61,"time":1751960211.6017628}
{"args":["GNU ld (GNU Binutils) 2.44\n","MATCHES","GNU ld .* 2\\.1[1-6]"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":62,"time":1751960211.6205349}
{"args":["CMAKE_LINK_GROUP_USING_RESCAN","LINKER:--start-group","LINKER:--end-group"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":70,"time":1751960211.6205349}
{"args":["CMAKE_LINK_GROUP_USING_RESCAN_SUPPORTED","TRUE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":71,"time":1751960211.6205349}
{"args":["__windows_compiler_gnu","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":74,"time":1751960211.6205349}
{"args":["__windows_compiler_gnu_abi","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":170,"time":1751960211.6205349}
{"args":["C"],"cmd":"__windows_compiler_gnu","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake","frame":3,"global_frame":3,"line":2,"time":1751960211.6215401}
{"args":["CMAKE_C_ARCHIVE_CREATE","<CMAKE_AR> qc <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":77,"time":1751960211.6215401}
{"args":["CMAKE_C_ARCHIVE_APPEND","<CMAKE_AR> q <TARGET> <LINK_FLAGS> <OBJECTS>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":78,"time":1751960211.6215401}
{"args":["CMAKE_C_ARCHIVE_FINISH","<CMAKE_RANLIB> <TARGET>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":79,"time":1751960211.6215401}
{"args":["type","SHARED_LIBRARY","SHARED_MODULE","EXE"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":85,"time":1751960211.6215401}
{"args":["CMAKE_SHARED_LIBRARY_LINK_STATIC_C_FLAGS","-Wl,-Bstatic"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":86,"time":1751960211.6215401}
{"args":["CMAKE_SHARED_LIBRARY_LINK_DYNAMIC_C_FLAGS","-Wl,-Bdynamic"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":87,"time":1751960211.6215401}
{"args":["CMAKE_SHARED_MODULE_LINK_STATIC_C_FLAGS","-Wl,-Bstatic"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":86,"time":1751960211.6215401}
{"args":["CMAKE_SHARED_MODULE_LINK_DYNAMIC_C_FLAGS","-Wl,-Bdynamic"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":87,"time":1751960211.6215401}
{"args":["CMAKE_EXE_LINK_STATIC_C_FLAGS","-Wl,-Bstatic"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":86,"time":1751960211.6215401}
{"args":["CMAKE_EXE_LINK_DYNAMIC_C_FLAGS","-Wl,-Bdynamic"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":87,"time":1751960211.6215401}
{"args":["CMAKE_C_VERBOSE_LINK_FLAG","-Wl,-v"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":90,"time":1751960211.6215401}
{"args":["CMAKE_C_USING_LINKER_SYSTEM",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":93,"time":1751960211.6215401}
{"args":["CMAKE_C_USING_LINKER_BFD","-fuse-ld=bfd"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":94,"time":1751960211.6215401}
{"args":["CMAKE_C_USING_LINKER_LLD","-fuse-ld=lld"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":95,"time":1751960211.6215401}
{"args":["CMAKE_C_COMPILE_OPTIONS_PIC",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":98,"time":1751960211.6215401}
{"args":["CMAKE_C_COMPILE_OPTIONS_PIE",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":99,"time":1751960211.6215401}
{"args":["_CMAKE_C_PIE_MAY_BE_SUPPORTED_BY_LINKER","NO"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":100,"time":1751960211.6215401}
{"args":["CMAKE_C_LINK_OPTIONS_PIE",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":101,"time":1751960211.6215401}
{"args":["CMAKE_C_LINK_OPTIONS_NO_PIE",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":102,"time":1751960211.6215401}
{"args":["CMAKE_SHARED_LIBRARY_C_FLAGS",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":103,"time":1751960211.6215401}
{"args":["NOT","CMAKE_GENERATOR","STREQUAL","MSYS Makefiles","OR","CMAKE_NEED_RESPONSE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":105,"time":1751960211.6215401}
{"args":["CMAKE_C_USE_RESPONSE_FILE_FOR_OBJECTS","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":106,"time":1751960211.6215401}
{"args":["CMAKE_C_USE_RESPONSE_FILE_FOR_LIBRARIES","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":107,"time":1751960211.6215401}
{"args":["CMAKE_C_USE_RESPONSE_FILE_FOR_INCLUDES","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":108,"time":1751960211.6215401}
{"args":["COMMAND","C:/msys64/mingw64/bin/cc.EXE","--version","OUTPUT_VARIABLE","_ver","ERROR_VARIABLE","_ver"],"cmd":"execute_process","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":112,"time":1751960211.6215401}
{"args":["cc.EXE (Rev6, Built by MSYS2 project) 15.1.0\nCopyright (C) 2025 Free Software Foundation, Inc.\nThis is free software; see the source for copying conditions.  There is NO\nwarranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.\n\n","MATCHES","\\(GCC\\) 3\\."],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":113,"time":1751960211.6355667}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":127,"time":1751960211.6355667}
{"args":["CMAKE_C_RESPONSE_FILE_LINK_FLAG","@"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":129,"time":1751960211.6355667}
{"args":["CMAKE_C_CREATE_SHARED_MODULE","<CMAKE_C_COMPILER> <CMAKE_SHARED_MODULE_C_FLAGS> <LANGUAGE_COMPILE_FLAGS> <LINK_FLAGS> <CMAKE_SHARED_MODULE_CREATE_C_FLAGS> -o <TARGET> -Wl,--major-image-version,<TARGET_VERSION_MAJOR>,--minor-image-version,<TARGET_VERSION_MINOR> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":133,"line_end":134,"time":1751960211.6355667}
{"args":["CMAKE_C_CREATE_SHARED_LIBRARY","<CMAKE_C_COMPILER> <CMAKE_SHARED_LIBRARY_C_FLAGS> <LANGUAGE_COMPILE_FLAGS> <LINK_FLAGS> <CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS> -o <TARGET> -Wl,--out-implib,<TARGET_IMPLIB> -Wl,--major-image-version,<TARGET_VERSION_MAJOR>,--minor-image-version,<TARGET_VERSION_MINOR> <OBJECTS> <LINK_LIBRARIES>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":135,"line_end":136,"time":1751960211.6355667}
{"args":["CMAKE_C_LINK_EXECUTABLE","<CMAKE_C_COMPILER> <FLAGS> <CMAKE_C_LINK_FLAGS> <LINK_FLAGS> <OBJECTS> -o <TARGET> -Wl,--out-implib,<TARGET_IMPLIB> -Wl,--major-image-version,<TARGET_VERSION_MAJOR>,--minor-image-version,<TARGET_VERSION_MINOR> <LINK_LIBRARIES>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":137,"line_end":138,"time":1751960211.6355667}
{"args":["CMAKE_C_CREATE_WIN32_EXE","-mwindows"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":139,"time":1751960211.6355667}
{"args":["APPEND","CMAKE_C_ABI_FILES","Platform/Windows-GNU-C-ABI"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":141,"time":1751960211.6355667}
{"args":["@","STREQUAL","@","AND","NOT","CMAKE_GENERATOR","MATCHES","Ninja"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":146,"time":1751960211.6355667}
{"args":["NOT","CMAKE_RC_COMPILER_INIT","AND","NOT","CMAKE_GENERATOR_RC"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":162,"time":1751960211.6355667}
{"args":["_CMAKE_RC_COMPILER_LIST","windres","windres"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":163,"time":1751960211.6355667}
{"args":["_CMAKE_RC_COMPILER_FALLBACK","windres"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":164,"time":1751960211.6355667}
{"args":["RC"],"cmd":"enable_language","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":167,"time":1751960211.6355667}
{"args":["NOT","CMAKE_RC_COMPILER"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":11,"time":1751960211.6355667}
{"args":["NOT","","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":13,"time":1751960211.6355667}
{"args":["CMAKE_GENERATOR_RC"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":25,"time":1751960211.6355667}
{"args":["CMAKE_RC_COMPILER_INIT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":32,"time":1751960211.6355667}
{"args":["NOT","_CMAKE_RC_COMPILER_LIST"],"cmd":"elseif","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":35,"time":1751960211.6355667}
{"args":["CMAKE_RC_COMPILER","NAMES","windres;windres","DOC","RC compiler"],"cmd":"find_program","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":40,"time":1751960211.6355667}
{"args":["_CMAKE_RC_COMPILER_FALLBACK","AND","NOT","CMAKE_RC_COMPILER"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":41,"time":1751960211.6365852}
{"args":["_CMAKE_RC_COMPILER_FALLBACK"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":44,"time":1751960211.6365852}
{"args":["_CMAKE_RC_COMPILER_LIST"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":45,"time":1751960211.6365852}
{"args":["CMAKE_RC_COMPILER"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":48,"time":1751960211.6365852}
{"args":["_CMAKE_RC_COMPILER_NAME_WE","C:/msys64/mingw64/bin/windres.exe","NAME_WE"],"cmd":"get_filename_component","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":50,"time":1751960211.6365852}
{"args":["_CMAKE_RC_COMPILER_NAME_WE","STREQUAL","windres"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":51,"time":1751960211.6365852}
{"args":["CMAKE_RC_OUTPUT_EXTENSION",".obj"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":52,"time":1751960211.6365852}
{"args":["C:/msys64/mingw64/share/cmake/Modules/CMakeRCCompiler.cmake.in","C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeRCCompiler.cmake"],"cmd":"configure_file","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":58,"line_end":59,"time":1751960211.6365852}
{"args":["CMAKE_RC_COMPILER_ENV_VAR","RC"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake","frame":5,"global_frame":5,"line":60,"time":1751960211.6376228}
{"args":["CMAKE_RC_COMPILER","C:/msys64/mingw64/bin/windres.exe"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeRCCompiler.cmake","frame":5,"global_frame":5,"line":1,"time":1751960211.6401443}
{"args":["CMAKE_RC_COMPILER_ARG1",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeRCCompiler.cmake","frame":5,"global_frame":5,"line":2,"time":1751960211.6401443}
{"args":["CMAKE_RC_COMPILER_LOADED","1"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeRCCompiler.cmake","frame":5,"global_frame":5,"line":3,"time":1751960211.6401443}
{"args":["CMAKE_RC_SOURCE_FILE_EXTENSIONS","rc;RC"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeRCCompiler.cmake","frame":5,"global_frame":5,"line":4,"time":1751960211.6401443}
{"args":["CMAKE_RC_OUTPUT_EXTENSION",".obj"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeRCCompiler.cmake","frame":5,"global_frame":5,"line":5,"time":1751960211.6401443}
{"args":["CMAKE_RC_COMPILER_ENV_VAR","RC"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeRCCompiler.cmake","frame":5,"global_frame":5,"line":6,"time":1751960211.6401443}
{"args":["CMAKE_BASE_NAME"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":10,"time":1751960211.6401443}
{"args":["CMAKE_RC_COMPILER","MATCHES","windres[^/]*$"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":11,"time":1751960211.6401443}
{"args":["CMAKE_BASE_NAME","windres"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":12,"time":1751960211.6401443}
{"args":["CMAKE_SYSTEM_AND_RC_COMPILER_INFO_FILE","C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":16,"line_end":17,"time":1751960211.6401443}
{"args":["Platform/Windows-windres","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":18,"time":1751960211.6401443}
{"args":["CMAKE_RC_COMPILE_OBJECT","<CMAKE_RC_COMPILER> -O coff <DEFINES> <INCLUDES> <FLAGS> <SOURCE> <OBJECT>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake","frame":6,"global_frame":6,"line":1,"time":1751960211.6401443}
{"args":["CMAKE_USER_MAKE_RULES_OVERRIDE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":25,"time":1751960211.6401443}
{"args":["CMAKE_RC_FLAGS_INIT"," "],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":31,"time":1751960211.6401443}
{"args":["CMAKE_RC_FLAGS","Flags for Windows Resource Compiler"],"cmd":"cmake_initialize_per_config_variable","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":33,"time":1751960211.6401443}
{"args":["STRIP"," ","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":9,"time":1751960211.6401443}
{"args":["CMAKE_RC_FLAGS","","CACHE","STRING","Flags for Windows Resource Compiler during all build types."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":10,"line_end":11,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":12,"time":1751960211.6411514}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":14,"time":1751960211.6411514}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":15,"time":1751960211.6411514}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":17,"time":1751960211.6411514}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":18,"time":1751960211.6411514}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":20,"time":1751960211.6411514}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":21,"time":1751960211.6411514}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":22,"line_end":23,"time":1751960211.6411514}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":25,"time":1751960211.6411514}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":28,"time":1751960211.6411514}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":29,"time":1751960211.6411514}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":30,"time":1751960211.6411514}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":31,"time":1751960211.6411514}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":32,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS_DEBUG","","CACHE","STRING","Flags for Windows Resource Compiler during DEBUG builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":33,"line_end":34,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":35,"time":1751960211.6411514}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":30,"time":1751960211.6411514}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":31,"time":1751960211.6411514}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":32,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS_RELEASE","","CACHE","STRING","Flags for Windows Resource Compiler during RELEASE builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":33,"line_end":34,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":35,"time":1751960211.6411514}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":30,"time":1751960211.6411514}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":31,"time":1751960211.6411514}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":32,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS_MINSIZEREL","","CACHE","STRING","Flags for Windows Resource Compiler during MINSIZEREL builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":33,"line_end":34,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":35,"time":1751960211.6411514}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":30,"time":1751960211.6411514}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":31,"time":1751960211.6411514}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":32,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags for Windows Resource Compiler during RELWITHDEBINFO builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":33,"line_end":34,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":6,"global_frame":6,"line":35,"time":1751960211.6411514}
{"args":["CMAKE_RC_FLAG_REGEX","^[-/](D|I)"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":38,"time":1751960211.6411514}
{"args":["CMAKE_INCLUDE_FLAG_RC","-I "],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":42,"time":1751960211.6411514}
{"args":["NOT","CMAKE_RC_COMPILE_OBJECT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":44,"time":1751960211.6411514}
{"args":["CMAKE_RC_USE_LINKER_INFORMATION","FALSE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":49,"time":1751960211.6411514}
{"args":["CMAKE_RC_INFORMATION_LOADED","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake","frame":5,"global_frame":5,"line":52,"time":1751960211.6411514}
{"args":["CMAKE_RC_COMPILER_WORKS","1","CACHE","INTERNAL",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeTestRCCompiler.cmake","frame":5,"global_frame":5,"line":13,"time":1751960211.6411514}
{"args":["NOT","_INCLUDED_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":51,"time":1751960211.6411514}
{"args":["CMAKE_C_COMPILER_WRAPPER"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":57,"time":1751960211.6411514}
{"args":["NOT","_INCLUDED_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":65,"time":1751960211.6411514}
{"args":["CMAKE_C_SIZEOF_DATA_PTR"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":69,"time":1751960211.6411514}
{"args":["f","IN","LISTS","CMAKE_C_ABI_FILES"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":70,"time":1751960211.6411514}
{"args":["Platform/Windows-GNU-C-ABI"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":71,"time":1751960211.6411514}
{"args":["C"],"cmd":"__windows_compiler_gnu_abi","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake","frame":3,"global_frame":3,"line":1,"time":1751960211.6421471}
{"args":["CMAKE_NO_GNUtoMS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":171,"time":1751960211.6421471}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":173,"time":1751960211.6421471}
{"args":["CMAKE_GNUtoMS","Convert GNU import libraries to MS format (requires Visual Studio)","OFF"],"cmd":"option","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":174,"time":1751960211.6421471}
{"args":["CMAKE_GNUtoMS","AND","NOT","CMAKE_GNUtoMS_LIB"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":177,"time":1751960211.6421471}
{"args":["CMAKE_GNUtoMS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake","frame":4,"global_frame":4,"line":233,"time":1751960211.6421471}
{"args":["CMAKE_C_ABI_FILES"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":73,"time":1751960211.6421471}
{"args":["CMAKE_USER_MAKE_RULES_OVERRIDE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":82,"time":1751960211.6421471}
{"args":["CMAKE_USER_MAKE_RULES_OVERRIDE_C"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":88,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_INIT","  "],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":94,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS","Flags used by the C compiler"],"cmd":"cmake_initialize_per_config_variable","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":96,"time":1751960211.6421471}
{"args":["STRIP","  ","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":9,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS","","CACHE","STRING","Flags used by the C compiler during all build types."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":10,"line_end":11,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":12,"time":1751960211.6421471}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":14,"time":1751960211.6421471}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":15,"time":1751960211.6421471}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":17,"time":1751960211.6421471}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":18,"time":1751960211.6421471}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":20,"time":1751960211.6421471}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":21,"time":1751960211.6421471}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":22,"line_end":23,"time":1751960211.6421471}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":25,"time":1751960211.6421471}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":28,"time":1751960211.6421471}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":29,"time":1751960211.6421471}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":30,"time":1751960211.6421471}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":31,"time":1751960211.6421471}
{"args":["STRIP"," -g","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":32,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_DEBUG","-g","CACHE","STRING","Flags used by the C compiler during DEBUG builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":33,"line_end":34,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":35,"time":1751960211.6421471}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":30,"time":1751960211.6421471}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":31,"time":1751960211.6421471}
{"args":["STRIP"," -O3 -DNDEBUG","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":32,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_RELEASE","-O3 -DNDEBUG","CACHE","STRING","Flags used by the C compiler during RELEASE builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":33,"line_end":34,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":35,"time":1751960211.6421471}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":30,"time":1751960211.6421471}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":31,"time":1751960211.6421471}
{"args":["STRIP"," -Os -DNDEBUG","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":32,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_MINSIZEREL","-Os -DNDEBUG","CACHE","STRING","Flags used by the C compiler during MINSIZEREL builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":33,"line_end":34,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":35,"time":1751960211.6421471}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":30,"time":1751960211.6421471}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":31,"time":1751960211.6421471}
{"args":["STRIP"," -O2 -g -DNDEBUG","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":32,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_RELWITHDEBINFO","-O2 -g -DNDEBUG","CACHE","STRING","Flags used by the C compiler during RELWITHDEBINFO builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":33,"line_end":34,"time":1751960211.6421471}
{"args":["CMAKE_C_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":3,"global_frame":3,"line":35,"time":1751960211.6421471}
{"args":["CMAKE_C_STANDARD_LIBRARIES_INIT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":98,"time":1751960211.6421471}
{"args":["CMAKE_C_STANDARD_LIBRARIES","-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32","CACHE","STRING","Libraries linked by default with all C applications."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":99,"line_end":100,"time":1751960211.6421471}
{"args":["CMAKE_C_STANDARD_LIBRARIES"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":101,"time":1751960211.6421471}
{"args":["NOT","CMAKE_C_COMPILER_LAUNCHER","AND","DEFINED","ENV{CMAKE_C_COMPILER_LAUNCHER}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":104,"time":1751960211.6421471}
{"args":["NOT","CMAKE_C_LINKER_LAUNCHER","AND","DEFINED","ENV{CMAKE_C_LINKER_LAUNCHER}"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":109,"time":1751960211.6421471}
{"args":["CMakeCommonLanguageInclude"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":114,"time":1751960211.6421471}
{"args":["APPEND","CMAKE_EXE_LINKER_FLAGS_INIT"," "],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":9,"time":1751960211.6421471}
{"args":["APPEND","CMAKE_SHARED_LINKER_FLAGS_INIT"," "],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":10,"time":1751960211.6421471}
{"args":["APPEND","CMAKE_MODULE_LINKER_FLAGS_INIT"," "],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":11,"time":1751960211.6421471}
{"args":["CMAKE_EXE_LINKER_FLAGS","Flags used by the linker"],"cmd":"cmake_initialize_per_config_variable","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":13,"time":1751960211.6421471}
{"args":["STRIP"," ","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":9,"time":1751960211.6421471}
{"args":["CMAKE_EXE_LINKER_FLAGS","","CACHE","STRING","Flags used by the linker during all build types."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":10,"line_end":11,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":12,"time":1751960211.6431465}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":14,"time":1751960211.6431465}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":15,"time":1751960211.6431465}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":17,"time":1751960211.6431465}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":18,"time":1751960211.6431465}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":20,"time":1751960211.6431465}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":21,"time":1751960211.6431465}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":22,"line_end":23,"time":1751960211.6431465}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":25,"time":1751960211.6431465}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":28,"time":1751960211.6431465}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":29,"time":1751960211.6431465}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS_DEBUG","","CACHE","STRING","Flags used by the linker during DEBUG builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS_RELEASE","","CACHE","STRING","Flags used by the linker during RELEASE builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS_MINSIZEREL","","CACHE","STRING","Flags used by the linker during MINSIZEREL builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags used by the linker during RELWITHDEBINFO builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS","Flags used by the linker during the creation of shared libraries"],"cmd":"cmake_initialize_per_config_variable","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":14,"time":1751960211.6431465}
{"args":["STRIP"," ","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":9,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during all build types."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":10,"line_end":11,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":12,"time":1751960211.6431465}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":14,"time":1751960211.6431465}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":15,"time":1751960211.6431465}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":17,"time":1751960211.6431465}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":18,"time":1751960211.6431465}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":20,"time":1751960211.6431465}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":21,"time":1751960211.6431465}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":22,"line_end":23,"time":1751960211.6431465}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":25,"time":1751960211.6431465}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":28,"time":1751960211.6431465}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":29,"time":1751960211.6431465}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS_DEBUG","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during DEBUG builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS_RELEASE","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during RELEASE builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["CMAKE_MODULE_LINKER_FLAGS","Flags used by the linker during the creation of modules"],"cmd":"cmake_initialize_per_config_variable","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":15,"time":1751960211.6431465}
{"args":["STRIP"," ","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":9,"time":1751960211.6431465}
{"args":["CMAKE_MODULE_LINKER_FLAGS","","CACHE","STRING","Flags used by the linker during the creation of modules during all build types."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":10,"line_end":11,"time":1751960211.6431465}
{"args":["CMAKE_MODULE_LINKER_FLAGS"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":12,"time":1751960211.6431465}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":14,"time":1751960211.6431465}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":15,"time":1751960211.6431465}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":17,"time":1751960211.6431465}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":18,"time":1751960211.6431465}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":20,"time":1751960211.6431465}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":21,"time":1751960211.6431465}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":22,"line_end":23,"time":1751960211.6431465}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":25,"time":1751960211.6431465}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":28,"time":1751960211.6431465}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":29,"time":1751960211.6431465}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_MODULE_LINKER_FLAGS_DEBUG","","CACHE","STRING","Flags used by the linker during the creation of modules during DEBUG builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_MODULE_LINKER_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6431465}
{"args":["CMAKE_MODULE_LINKER_FLAGS_RELEASE","","CACHE","STRING","Flags used by the linker during the creation of modules during RELEASE builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6431465}
{"args":["CMAKE_MODULE_LINKER_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6431465}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6431465}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6431465}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6441441}
{"args":["CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL","","CACHE","STRING","Flags used by the linker during the creation of modules during MINSIZEREL builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6441441}
{"args":["CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6441441}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6441441}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6441441}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6441441}
{"args":["CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6441441}
{"args":["CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS","Flags used by the linker during the creation of static libraries"],"cmd":"cmake_initialize_per_config_variable","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":16,"time":1751960211.6441441}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":9,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS","","CACHE","STRING","Flags used by the linker during the creation of static libraries during all build types."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":10,"line_end":11,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":12,"time":1751960211.6441441}
{"args":["NOT","CMAKE_NOT_USING_CONFIG_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":14,"time":1751960211.6441441}
{"args":["_CONFIGS","Debug","Release","MinSizeRel","RelWithDebInfo"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":15,"time":1751960211.6441441}
{"args":["_GENERATOR_IS_MULTI_CONFIG","GLOBAL","PROPERTY","GENERATOR_IS_MULTI_CONFIG"],"cmd":"get_property","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":17,"time":1751960211.6441441}
{"args":["_GENERATOR_IS_MULTI_CONFIG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":18,"time":1751960211.6441441}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":20,"time":1751960211.6441441}
{"args":["NOT","CMAKE_NO_BUILD_TYPE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":21,"time":1751960211.6441441}
{"args":["CMAKE_BUILD_TYPE","","CACHE","STRING","Choose the type of build, options are: None Debug Release RelWithDebInfo MinSizeRel ..."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":22,"line_end":23,"time":1751960211.6441441}
{"args":["APPEND","_CONFIGS",""],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":25,"time":1751960211.6441441}
{"args":["REMOVE_DUPLICATES","_CONFIGS"],"cmd":"list","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":28,"time":1751960211.6441441}
{"args":["_BUILD_TYPE","IN","LISTS","_CONFIGS"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":29,"time":1751960211.6441441}
{"args":["NOT","Debug","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6441441}
{"args":["TOUPPER","Debug","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6441441}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS_DEBUG","","CACHE","STRING","Flags used by the linker during the creation of static libraries during DEBUG builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS_DEBUG"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6441441}
{"args":["NOT","Release","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6441441}
{"args":["TOUPPER","Release","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6441441}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS_RELEASE","","CACHE","STRING","Flags used by the linker during the creation of static libraries during RELEASE builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS_RELEASE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6441441}
{"args":["NOT","MinSizeRel","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6441441}
{"args":["TOUPPER","MinSizeRel","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6441441}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL","","CACHE","STRING","Flags used by the linker during the creation of static libraries during MINSIZEREL builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6441441}
{"args":["NOT","RelWithDebInfo","STREQUAL",""],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":30,"time":1751960211.6441441}
{"args":["TOUPPER","RelWithDebInfo","_BUILD_TYPE"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":31,"time":1751960211.6441441}
{"args":["STRIP","","_INIT"],"cmd":"string","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":32,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO","","CACHE","STRING","Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":33,"line_end":34,"time":1751960211.6441441}
{"args":["CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake","frame":4,"global_frame":4,"line":35,"time":1751960211.6441441}
{"args":["CMAKE_BUILD_TOOL","C:/msys64/mingw64/bin/ninja.exe"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":19,"time":1751960211.6441441}
{"args":["CMAKE_VERBOSE_MAKEFILE"],"cmd":"mark_as_advanced","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":21,"line_end":23,"time":1751960211.6441441}
{"args":["_cmake_common_language_platform_flags","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":27,"time":1751960211.6441441}
{"args":["C"],"cmd":"_cmake_common_language_platform_flags","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":115,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":28,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_C_COMPILE_OPTIONS_PIC"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":32,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_C_COMPILE_OPTIONS_PIE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":36,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_C_LINK_OPTIONS_PIE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":39,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_C_LINK_OPTIONS_NO_PIE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":42,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_C_COMPILE_OPTIONS_DLL"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":46,"time":1751960211.6441441}
{"args":["CMAKE_C_COMPILE_OPTIONS_DLL",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":47,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":50,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_LINK_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":54,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":58,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_RUNTIME_C_FLAG_SEP"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":62,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_RPATH_LINK_C_FLAG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":66,"time":1751960211.6441441}
{"args":["CMAKE_SHARED_LIBRARY_RPATH_LINK_C_FLAG",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":67,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_EXE_EXPORTS_C_FLAG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":70,"time":1751960211.6441441}
{"args":["CMAKE_EXE_EXPORTS_C_FLAG",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":71,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_SONAME_C_FLAG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":74,"time":1751960211.6441441}
{"args":["CMAKE_SHARED_LIBRARY_SONAME_C_FLAG",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":75,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_EXECUTABLE_RUNTIME_C_FLAG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":78,"time":1751960211.6441441}
{"args":["CMAKE_EXECUTABLE_RUNTIME_C_FLAG",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":79,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_EXECUTABLE_RUNTIME_C_FLAG_SEP"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":82,"time":1751960211.6441441}
{"args":["CMAKE_EXECUTABLE_RUNTIME_C_FLAG_SEP",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":83,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_EXECUTABLE_RPATH_LINK_C_FLAG"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":86,"time":1751960211.6441441}
{"args":["CMAKE_EXECUTABLE_RPATH_LINK_C_FLAG",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":87,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_LINK_C_WITH_RUNTIME_PATH"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":90,"time":1751960211.6441441}
{"args":["CMAKE_SHARED_LIBRARY_LINK_C_WITH_RUNTIME_PATH",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":91,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_INCLUDE_FLAG_C"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":94,"time":1751960211.6441441}
{"args":["NOT","CMAKE_MODULE_EXISTS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":101,"time":1751960211.6441441}
{"args":["CMAKE_SHARED_MODULE_C_FLAGS",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":102,"time":1751960211.6441441}
{"args":["CMAKE_SHARED_MODULE_CREATE_C_FLAGS","-shared"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":103,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_MODULE_CREATE_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":106,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_MODULE_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":109,"time":1751960211.6441441}
{"args":["CMAKE_SHARED_MODULE_C_FLAGS",""],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":110,"time":1751960211.6441441}
{"args":["type","IN","ITEMS","SHARED_LIBRARY","SHARED_MODULE","EXE"],"cmd":"foreach","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":113,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_LINK_STATIC_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":114,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_LIBRARY_LINK_DYNAMIC_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":118,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_MODULE_LINK_STATIC_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":114,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_SHARED_MODULE_LINK_DYNAMIC_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":118,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_EXE_LINK_STATIC_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":114,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_EXE_LINK_DYNAMIC_C_FLAGS"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake","frame":3,"global_frame":3,"line":118,"time":1751960211.6441441}
{"args":["NOT","CMAKE_C_CREATE_SHARED_LIBRARY"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":145,"time":1751960211.6441441}
{"args":["NOT","CMAKE_C_CREATE_SHARED_MODULE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":151,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_C_ARCHIVE_CREATE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":157,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_C_ARCHIVE_APPEND"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":160,"time":1751960211.6441441}
{"args":["NOT","DEFINED","CMAKE_C_ARCHIVE_FINISH"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":163,"time":1751960211.6441441}
{"args":["NOT","CMAKE_C_COMPILE_OBJECT"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":168,"time":1751960211.6441441}
{"args":["CMAKE_C_COMPILE_OBJECT","<CMAKE_C_COMPILER> <DEFINES> <INCLUDES> <FLAGS> -o <OBJECT> -c <SOURCE>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":169,"line_end":170,"time":1751960211.6441441}
{"args":["NOT","CMAKE_C_LINK_EXECUTABLE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":173,"time":1751960211.6441441}
{"args":["CMAKE_C_USE_LINKER_INFORMATION","TRUE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":178,"time":1751960211.6441441}
{"args":["CMAKE_C_INFORMATION_LOADED","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake","frame":2,"global_frame":2,"line":180,"time":1751960211.6441441}
{"args":["CMAKE_C_COMPILER_FORCED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake","frame":2,"global_frame":2,"line":5,"time":1751960211.6441441}
{"args":["CMAKE_C_COMPILER_WORKS","TRUE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake","frame":2,"global_frame":2,"line":8,"time":1751960211.6441441}
{"args":[],"cmd":"return","file":"C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake","frame":2,"global_frame":2,"line":9,"time":1751960211.6441441}
{"args":["Internal/CMakeCommonLinkerInformation"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":11,"time":1751960211.6441441}
{"args":["_cmake_common_linker_platform_flags","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake","frame":3,"global_frame":3,"line":8,"time":1751960211.6456566}
{"args":["_INCLUDED_FILE","0"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":13,"time":1751960211.6456566}
{"args":["CMAKE_C_COMPILER_LINKER_ID"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":16,"time":1751960211.6456566}
{"args":["Linker/GNU-C","OPTIONAL"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":17,"time":1751960211.6456566}
{"args":["Linker/GNU"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU-C.cmake","frame":3,"global_frame":3,"line":4,"time":1751960211.6456566}
{"args":[],"cmd":"include_guard","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":6,"time":1751960211.6456566}
{"args":["SCOPE_FOR","POLICIES"],"cmd":"block","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":8,"time":1751960211.6456566}
{"args":["SET","CMP0140","NEW"],"cmd":"cmake_policy","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":9,"time":1751960211.6456566}
{"args":["__linker_gnu","lang"],"cmd":"function","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":11,"time":1751960211.6456566}
{"args":["C"],"cmd":"__linker_gnu","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU-C.cmake","frame":3,"global_frame":3,"line":6,"time":1751960211.6456566}
{"args":["CMAKE_C_LINKER_DEPFILE_FLAGS","LINKER:--dependency-file=<DEP_FILE>"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":13,"time":1751960211.6456566}
{"args":["CMAKE_C_LINKER_DEPFILE_FORMAT","gcc"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":14,"time":1751960211.6456566}
{"args":["NOT","CMAKE_EXECUTABLE_FORMAT","STREQUAL","ELF"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":16,"time":1751960211.6456566}
{"args":["CMAKE_C_LINKER_DEPFILE_SUPPORTED","FALSE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":18,"time":1751960211.6456566}
{"args":["NOT","DEFINED","CMAKE_C_LINKER_DEPFILE_SUPPORTED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":21,"time":1751960211.6456566}
{"args":["CMAKE_C_LINKER_DEPFILE_SUPPORTED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":52,"time":1751960211.6456566}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":54,"time":1751960211.6456566}
{"args":["CMAKE_C_LINK_DEPENDS_USE_LINKER","FALSE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":55,"time":1751960211.6456566}
{"args":["CMAKE_C_COMPILER_LINKER_ID","AND","CMAKE_C_COMPILER_LINKER_ID","STREQUAL","GNU","AND","CMAKE_C_COMPILER_LINKER_VERSION","VERSION_LESS","2.41"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":61,"line_end":63,"time":1751960211.6456566}
{"args":["CMAKE_C_LINK_OPTIONS_WARNING_AS_ERROR","LINKER:--fatal-warnings"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":68,"time":1751960211.6456566}
{"args":["PROPAGATE","CMAKE_C_LINKER_DEPFILE_FLAGS","CMAKE_C_LINKER_DEPFILE_FORMAT","CMAKE_C_LINKER_DEPFILE_SUPPORTED","CMAKE_C_LINK_DEPENDS_USE_LINKER","CMAKE_C_LINK_OPTIONS_WARNING_AS_ERROR"],"cmd":"return","file":"C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake","frame":4,"global_frame":4,"line":70,"line_end":74,"time":1751960211.6456566}
{"args":["CMAKE_SYSTEM_PROCESSOR","AND","CMAKE_C_COMPILER_LINKER_ID"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":21,"time":1751960211.6456566}
{"args":["Platform/Windows-GNU-C-AMD64","OPTIONAL","RESULT_VARIABLE","_INCLUDED_FILE"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":22,"time":1751960211.6456566}
{"args":["CMAKE_C_COMPILER_LINKER_ID"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":27,"time":1751960211.6466637}
{"args":["Platform/Linker/Windows-GNU-C","OPTIONAL","RESULT_VARIABLE","_INCLUDED_FILE"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":28,"line_end":29,"time":1751960211.6466637}
{"args":["Platform/Linker/Windows-GNU"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake","frame":3,"global_frame":3,"line":4,"time":1751960211.6466637}
{"args":[],"cmd":"include_guard","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake","frame":4,"global_frame":4,"line":6,"time":1751960211.6466637}
{"args":["Platform/Linker/GNU"],"cmd":"include","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake","frame":4,"global_frame":4,"line":8,"time":1751960211.6466637}
{"args":[],"cmd":"include_guard","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":6,"time":1751960211.6466637}
{"args":["__cmake_set_whole_archive_feature","__linker"],"cmd":"function","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":10,"time":1751960211.6466637}
{"args":["C:/msys64/mingw64/bin/ld.exe"],"cmd":"__cmake_set_whole_archive_feature","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":48,"time":1751960211.6466637}
{"args":["__lang"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":11,"time":1751960211.6466637}
{"args":["ARGC","EQUAL","2"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":12,"time":1751960211.6466637}
{"args":["NOT","__linker"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":16,"time":1751960211.6466637}
{"args":["NOT","DEFINED","CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":20,"time":1751960211.6466637}
{"args":["COMMAND","C:/msys64/mingw64/bin/ld.exe","--push-state","--pop-state","OUTPUT_VARIABLE","__linker_log","ERROR_VARIABLE","__linker_log"],"cmd":"execute_process","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":22,"line_end":24,"time":1751960211.6466637}
{"args":["__linker_log","MATCHES","--push-state","OR","__linker_log","MATCHES","--pop-state"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":25,"time":1751960211.6627808}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":27,"time":1751960211.6627808}
{"args":["CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED","TRUE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":28,"time":1751960211.6627808}
{"args":["CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED","TRUE","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":30,"time":1751960211.6627808}
{"args":["CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":33,"time":1751960211.6627808}
{"args":["CMAKE_LINK_LIBRARY_USING_WHOLE_ARCHIVE","LINKER:--push-state,--whole-archive","<LINK_ITEM>","LINKER:--pop-state","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":34,"line_end":36,"time":1751960211.6627808}
{"args":["CMAKE_LINK_LIBRARY_USING_WHOLE_ARCHIVE_SUPPORTED","TRUE","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":42,"time":1751960211.6627808}
{"args":["CMAKE_LINK_LIBRARY_WHOLE_ARCHIVE_ATTRIBUTES","LIBRARY_TYPE=STATIC","DEDUPLICATION=YES","OVERRIDE=DEFAULT","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":6,"global_frame":6,"line":43,"time":1751960211.6627808}
{"args":["__windows_linker_gnu","lang"],"cmd":"macro","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake","frame":4,"global_frame":4,"line":10,"time":1751960211.6627808}
{"args":["C"],"cmd":"__windows_linker_gnu","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake","frame":3,"global_frame":3,"line":6,"time":1751960211.6627808}
{"args":["CMAKE_C_PLATFORM_LINKER_ID","GNU"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake","frame":4,"global_frame":4,"line":11,"time":1751960211.6627808}
{"args":["CMAKE_C_COMPILER_LINKER"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake","frame":4,"global_frame":4,"line":12,"time":1751960211.6627808}
{"args":["C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe","C"],"cmd":"__cmake_set_whole_archive_feature","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake","frame":4,"global_frame":4,"line":13,"time":1751960211.6627808}
{"args":["__lang"],"cmd":"unset","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":11,"time":1751960211.6627808}
{"args":["ARGC","EQUAL","2"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":12,"time":1751960211.6627808}
{"args":["__lang","C_"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":13,"time":1751960211.6627808}
{"args":["NOT","__linker"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":16,"time":1751960211.6627808}
{"args":["NOT","DEFINED","CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":20,"time":1751960211.6627808}
{"args":["COMMAND","C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe","--push-state","--pop-state","OUTPUT_VARIABLE","__linker_log","ERROR_VARIABLE","__linker_log"],"cmd":"execute_process","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":22,"line_end":24,"time":1751960211.6627808}
{"args":["__linker_log","MATCHES","--push-state","OR","__linker_log","MATCHES","--pop-state"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":25,"time":1751960211.6778049}
{"args":[],"cmd":"else","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":27,"time":1751960211.6778049}
{"args":["CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED","TRUE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":28,"time":1751960211.6778049}
{"args":["CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED","TRUE","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":30,"time":1751960211.6778049}
{"args":["CMAKE_C_LINKER_PUSHPOP_STATE_SUPPORTED"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":33,"time":1751960211.6778049}
{"args":["CMAKE_C_LINK_LIBRARY_USING_WHOLE_ARCHIVE","LINKER:--push-state,--whole-archive","<LINK_ITEM>","LINKER:--pop-state","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":34,"line_end":36,"time":1751960211.6778049}
{"args":["CMAKE_C_LINK_LIBRARY_USING_WHOLE_ARCHIVE_SUPPORTED","TRUE","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":42,"time":1751960211.6778049}
{"args":["CMAKE_C_LINK_LIBRARY_WHOLE_ARCHIVE_ATTRIBUTES","LIBRARY_TYPE=STATIC","DEDUPLICATION=YES","OVERRIDE=DEFAULT","PARENT_SCOPE"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake","frame":5,"global_frame":5,"line":43,"time":1751960211.6778049}
{"args":["NOT","_INCLUDED_FILE"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":33,"time":1751960211.6778049}
{"args":["C"],"cmd":"_cmake_common_linker_platform_flags","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":37,"time":1751960211.6778049}
{"args":["CMAKE_EXECUTABLE_FORMAT","STREQUAL","ELF"],"cmd":"if","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake","frame":3,"global_frame":3,"line":10,"time":1751960211.6778049}
{"args":["CMAKE_C_LINKER_INFORMATION_LOADED","1"],"cmd":"set","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake","frame":2,"global_frame":2,"line":39,"time":1751960211.6778049}
{"args":["C:/msys64/mingw64/share/cmake/Modules/CMakeCCompiler.cmake.in","C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeFiles/4.0.3/CMakeCCompiler.cmake","@ONLY"],"cmd":"configure_file","file":"C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeInspectCLinker.cmake","frame":2,"global_frame":2,"line":5,"line_end":8,"time":1751960211.6788101}
{"args":["VERSION","4.0.3"],"cmd":"cmake_minimum_required","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":4,"time":1751960211.6808128}
{"args":["TMP_PATHS_LIST"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":6,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":7,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":8,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":9,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":10,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":11,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":12,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST","C:/msys64/mingw64;C:/Program Files;C:/Program Files (x86);C:/msys64/mingw64;C:/Program Files/MesonTemp"],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":13,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":14,"time":1751960211.6808128}
{"args":["APPEND","TMP_PATHS_LIST",""],"cmd":"list","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":15,"time":1751960211.6808128}
{"args":["LIB_ARCH_LIST"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":17,"time":1751960211.6808128}
{"args":["CMAKE_LIBRARY_ARCHITECTURE_REGEX"],"cmd":"if","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":18,"time":1751960211.6808128}
{"args":["MESON_ARCH_LIST",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":28,"time":1751960211.6808128}
{"args":["MESON_PATHS_LIST","C:/msys64/mingw64;C:/Program Files;C:/Program Files (x86);C:/msys64/mingw64;C:/Program Files/MesonTemp"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":29,"time":1751960211.6808128}
{"args":["MESON_CMAKE_ROOT","C:/msys64/mingw64/share/cmake"],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":30,"time":1751960211.6808128}
{"args":["MESON_CMAKE_SYSROOT",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":31,"time":1751960211.6808128}
{"args":["MESON_FIND_ROOT_PATH",""],"cmd":"set","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":32,"time":1751960211.6808128}
{"args":["STATUS","C:/msys64/mingw64;C:/Program Files;C:/Program Files (x86);C:/msys64/mingw64;C:/Program Files/MesonTemp"],"cmd":"message","file":"C:/Users/<USER>/Pictures/scrcpy/scrcpy-master/build-win64-native/meson-private/cmake_libavformat/CMakeLists.txt","frame":1,"global_frame":1,"line":34,"time":1751960211.6808128}
