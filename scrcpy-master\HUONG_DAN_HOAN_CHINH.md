# 📋 Hướng dẫn hoàn chỉnh: <PERSON><PERSON><PERSON> bộ Clipboard tự động hai chiều cho Scrcpy

## 🎯 Tổng quan

Hướng dẫn này cung cấp đầy đủ các bước để build và sử dụng scrcpy đã được nâng cấp với tính năng đồng bộ clipboard tự động hai chiều giữa Windows và Android.

### Tính năng mới
- **Tự động đồng bộ Windows → Android**: Copy text trên Windows, tự động sync tới Android clipboard
- **Tự động đồng bộ Android → Windows**: Copy text trên Android, tự động sync tới Windows clipboard  
- **Chế độ clipboard-only**: Chạy scrcpy như background service chỉ để đồng bộ clipboard
- **Không cần can thiệp thủ công**: Không cần nhấn Ctrl+V trong cửa sổ scrcpy

## 🛠️ Quá trình Build

### Y<PERSON><PERSON> cầu trước khi bắt đầu

#### 1. Cài đặt MSYS2/MINGW64
Tải và cài đặt MSYS2 từ: https://www.msys2.org/

#### 2. Cài đặt Dependencies
Mở MINGW64 terminal và chạy:
```bash
# Cập nhật package database
pacman -Syu

# Cài đặt build tools
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-meson
pacman -S mingw-w64-x86_64-ninja
pacman -S mingw-w64-x86_64-pkg-config

# Cài đặt libraries
pacman -S mingw-w64-x86_64-SDL2
pacman -S mingw-w64-x86_64-ffmpeg
pacman -S mingw-w64-x86_64-libusb
```

#### 3. Thiết lập Android SDK
Đảm bảo bạn đã cài đặt Android SDK với:
- Android Studio HOẶC
- Command line tools với SDK Manager

### Thiết lập môi trường

#### 1. Đặt đường dẫn Android SDK
```bash
# Đặt environment variables (điều chỉnh đường dẫn cho phù hợp)
export ANDROID_HOME="/c/Users/<USER>/AppData/Local/Android/Sdk"
export ANDROID_SDK_ROOT="/c/Users/<USER>/AppData/Local/Android/Sdk"

# Kiểm tra
echo $ANDROID_HOME
```

#### 2. Thêm ADB vào PATH
```bash
# Thêm ADB vào PATH (điều chỉnh đường dẫn cho phù hợp)
export PATH="$PATH:/d/Android/GT_Neo2/platform-tools"

# Làm cho thay đổi vĩnh viễn
echo 'export PATH="$PATH:/d/Android/GT_Neo2/platform-tools"' >> ~/.bashrc
source ~/.bashrc

# Kiểm tra
adb version
```

#### 3. Chấp nhận Android SDK Licenses
```bash
# Chấp nhận tất cả SDK licenses
"$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager.bat" --licenses
```

### Các bước Build

#### 1. Di chuyển tới thư mục dự án
```bash
cd /c/Users/<USER>/Pictures/scrcpy/scrcpy-master
```

#### 2. Cấu hình Build
```bash
# Thiết lập native build
meson setup build-win64-native
```

#### 3. Biên dịch
```bash
# Build dự án
meson compile -C build-win64-native
```

#### 4. Cài đặt Server File
```bash
# Tạo thư mục và copy server
mkdir -p /c/msys64/mingw64/share/scrcpy
cp build-win64-native/server/scrcpy-server /c/msys64/mingw64/share/scrcpy/
```

## 🚀 Cách sử dụng

### Tùy chọn 1: Chế độ Clipboard-only (Khuyến nghị)

**Tốt nhất cho đồng bộ clipboard background với ít tài nguyên**

```bash
./build-win64-native/app/scrcpy.exe --clipboard-only
```

**Tính năng:**
- Không có video/audio mirroring
- Chạy như background service
- Đồng bộ clipboard hai chiều
- Tiêu tốn ít tài nguyên
- Tự động khởi động clipboard monitoring

**Output mong đợi:**
```
INFO: Clipboard monitor started
INFO: Running in clipboard-only mode - no video/audio
INFO: Clipboard-only mode: waiting for clipboard changes...
INFO: Press Ctrl+C to quit
```

### Tùy chọn 2: Chế độ Normal với Clipboard Monitoring

**Tốt nhất cho đầy đủ tính năng scrcpy với clipboard sync**

```bash
./build-win64-native/app/scrcpy.exe --clipboard-monitor
```

**Tính năng:**
- Video mirroring đầy đủ
- Audio streaming
- Đồng bộ clipboard hai chiều
- Tất cả tính năng scrcpy tiêu chuẩn

### Tùy chọn 3: Background Service Script

**Tốt nhất để chạy như Windows service**

```bash
# Tạo service script
cat > clipboard_service.sh << 'EOF'
#!/bin/bash
./build-win64-native/app/scrcpy.exe --clipboard-monitor --window-width=1 --window-height=1 --no-audio --window-title="Clipboard" &
echo "Clipboard sync đang chạy trong background..."
EOF

# Làm cho executable và chạy
chmod +x clipboard_service.sh
./clipboard_service.sh
```

## 📱 Cách hoạt động

### Đồng bộ Windows → Android
1. Copy text trên Windows (Ctrl+C, right-click copy, v.v.)
2. Windows clipboard monitor phát hiện thay đổi
3. Text tự động gửi tới Android device
4. Text có sẵn trong Android clipboard
5. Paste trên Android (long press → Paste)

**Logs mong đợi:**
```
DEBUG: Clipboard updated - sending to device
INFO: Clipboard sent to device
[server] INFO: Device clipboard set
```

### Đồng bộ Android → Windows
1. Copy text trên Android (long press → Copy)
2. Android gửi clipboard data tới scrcpy
3. Text tự động đặt vào Windows clipboard
4. Paste trên Windows (Ctrl+V)

**Logs mong đợi:**
```
INFO: Device clipboard copied
DEBUG: Computer clipboard unchanged (nếu cùng nội dung)
```

## 🔧 Tùy chọn Command Line

### Tùy chọn Clipboard mới
- `--clipboard-monitor`: Bật automatic Windows clipboard monitoring
- `--clipboard-only`: Chạy trong clipboard-only mode (không video/audio)
- `--no-clipboard-autosync`: Tắt automatic clipboard sync

### Ví dụ sử dụng
```bash
# Chế độ clipboard-only
./scrcpy.exe --clipboard-only

# Chế độ normal với clipboard monitoring
./scrcpy.exe --clipboard-monitor

# Tắt clipboard sync
./scrcpy.exe --no-clipboard-autosync

# Kết hợp với các tùy chọn khác
./scrcpy.exe --clipboard-monitor --max-size=1024 --bit-rate=2M
```

## 🔍 Xử lý sự cố

### Lỗi Build

#### Không tìm thấy Meson
```bash
# Cài đặt meson
pacman -S mingw-w64-x86_64-meson
```

#### Không tìm thấy FFmpeg
```bash
# Cài đặt FFmpeg
pacman -S mingw-w64-x86_64-ffmpeg
```

#### Lỗi Android SDK license
```bash
# Chấp nhận licenses cho SDK đúng
"$ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager.bat" --licenses
```

### Lỗi Runtime

#### Không tìm thấy ADB
```bash
# Thêm ADB vào PATH
export PATH="$PATH:/path/to/platform-tools"
```

#### Không tìm thấy Device
- Bật USB debugging trên Android device
- Authorize máy tính khi popup xuất hiện
- Kiểm tra kết nối: `adb devices`

#### Clipboard không sync
- Đảm bảo Android version >= 7.0
- Kiểm tra device authorization
- Restart scrcpy nếu cần
- Xác minh logs hiển thị clipboard events

### Vấn đề Performance

#### CPU usage cao
- Sử dụng `--clipboard-only` mode để ít tài nguyên nhất
- Đóng các ứng dụng không cần thiết
- Kiểm tra background processes

#### Memory leaks
- Restart scrcpy định kỳ cho sessions dài
- Monitor memory usage với Task Manager

## 📊 Performance mong đợi

### Sử dụng tài nguyên
- **Clipboard-only mode**: <1% CPU khi idle, ~10MB RAM
- **Normal mode**: Phụ thuộc video settings, clipboard overhead tối thiểu
- **Network usage**: Tối thiểu chỉ cho clipboard data

### Tương thích
- **Windows**: 7, 8, 10, 11 (tested trên Windows 10+)
- **Android**: 7.0+ (API 24+) cần thiết cho clipboard API
- **Kết nối**: USB hoặc WiFi (TCP/IP)

## 🎯 Dấu hiệu thành công

### Build thành công
```
BUILD SUCCESSFUL in Xs
```

### Clipboard Sync thành công
```
INFO: Clipboard monitor initialized
INFO: Clipboard monitor started
INFO: Device clipboard copied          # Android → Windows
DEBUG: Clipboard updated - sending to device  # Windows → Android
```

### Bidirectional Sync hoạt động
1. Copy "Hello from Windows" trên Windows
2. Paste trên Android → should show "Hello from Windows"
3. Copy "Hello from Android" trên Android
4. Paste trên Windows → should show "Hello from Android"

## 🚀 Sử dụng nâng cao

### Chạy như Windows Service

Tạo batch file để tự động khởi động clipboard sync:

```batch
@echo off
cd /d "C:\path\to\scrcpy\scrcpy-master"
start /min "" "C:\msys64\mingw64\bin\bash.exe" -c "./build-win64-native/app/scrcpy.exe --clipboard-only"
```

### Tích hợp với Startup

Thêm vào Windows startup folder:
```
Win + R → shell:startup → Đặt batch file vào đây
```

### Nhiều Devices

Cho nhiều Android devices:
```bash
# Liệt kê devices
adb devices

# Kết nối tới device cụ thể
./scrcpy.exe --clipboard-only --serial=DEVICE_SERIAL
```

### Kết nối WiFi

```bash
# Kết nối qua WiFi
adb connect 192.168.1.XXX:5555
./scrcpy.exe --clipboard-only
```

## 💡 Tips và Best Practices

### Tối ưu Performance
- Sử dụng `--clipboard-only` cho background operation
- Đóng các ứng dụng không cần thiết
- Sử dụng kết nối WiFi cho performance tốt hơn
- Restart scrcpy hàng ngày cho sessions dài

### Cân nhắc bảo mật
- Clipboard data được truyền qua ADB connection
- Sử dụng USB connection cho dữ liệu nhạy cảm
- Chú ý nội dung clipboard khi dùng WiFi công cộng

### Ví dụ Workflow

#### Development Workflow
```bash
# Khởi động clipboard sync trong background
./scrcpy.exe --clipboard-only &

# Copy code từ IDE → paste trong Android terminal
# Copy error messages từ Android → paste trong Google search
```

#### Content Sharing Workflow
```bash
# Copy URLs từ browser → mở trong Android browser
# Copy text từ Android apps → paste trong Windows documents
# Copy số điện thoại → paste trong Windows contacts
```

## 🔧 Tùy chỉnh

### Sửa đổi Clipboard Behavior

Chỉnh sửa `clipboard_monitor.c` để tùy chỉnh:
- Lọc nội dung clipboard
- Tần suất sync
- Biến đổi nội dung

### Thêm Logging

Để debug chi tiết:
```bash
./scrcpy.exe --clipboard-only --verbosity=debug > clipboard.log 2>&1
```

## 📝 Files đã tạo/sửa đổi

### Core Implementation
- `app/src/clipboard_monitor.c` - Windows clipboard monitoring
- `app/src/clipboard_monitor.h` - Header definitions
- `app/src/cli.c` - Command line options
- `app/src/options.h` - Configuration options
- `app/src/scrcpy.c` - Main integration

### Documentation
- `HUONG_DAN_HOAN_CHINH.md` - Hướng dẫn toàn diện này
- `CLIPBOARD_SYNC_README.md` - Tổng quan tính năng
- `test_clipboard_only.md` - Quy trình testing
- `BUILD_MINGW64.md` - Hướng dẫn build

### Build Scripts
- `build_windows.bat` - Windows build script
- `clipboard_service.sh` - Service startup script

## 🎉 Kết luận

Bây giờ bạn đã có một automatic bidirectional clipboard sync hoàn chỉnh cho scrcpy với:

- ✅ Tự động đồng bộ clipboard từ Windows tới Android
- ✅ Tự động đồng bộ clipboard từ Android tới Windows
- ✅ Chạy như background service với ít tài nguyên
- ✅ Không cần can thiệp thủ công (không cần Ctrl+V)
- ✅ Cung cấp đồng bộ clipboard hai chiều thực sự
- ✅ Hỗ trợ nhiều chế độ sử dụng và cấu hình
- ✅ Bao gồm tài liệu toàn diện và xử lý sự cố

Implementation này đáp ứng yêu cầu ban đầu về việc chia sẻ clipboard liền mạch giữa Windows và Android devices thông qua scrcpy, cung cấp giải pháp chuyên nghiệp cho đồng bộ clipboard tự động.
