# Test Script cho Clipboard-Only Mode

## Mô tả
Test script nà<PERSON> gi<PERSON><PERSON> kiểm tra tính năng clipboard-only mode của scrcpy với automatic bidirectional clipboard sync.

## Yêu cầu
1. Android device được kết nối qua USB hoặc WiFi
2. ADB đã được cài đặt và device được authorize
3. Scrcpy đã được build với tính năng clipboard monitor

## Test Cases

### Test 1: Khởi động Clipboard-Only Mode
```bash
# Chạy scrcpy trong clipboard-only mode
scrcpy --clipboard-only

# Expected behavior:
# - Không có cửa sổ video hiển thị
# - Console hiển thị "Running in clipboard-only mode - no video/audio"
# - Console hiển thị "Clipboard monitoring started"
# - Console hiển thị "Clipboard-only mode: waiting for clipboard changes..."
```

### Test 2: Windows to Android Clipboard Sync
```
1. Khởi động scrcpy với --clipboard-only
2. Copy text trên Windows (Ctrl+C hoặc right-click copy)
3. Mở app text editor trên Android (như Notes)
4. Paste (long press -> Paste)

Expected: Text từ Windows xuất hiện trên Android
```

### Test 3: Android to Windows Clipboard Sync  
```
1. Khởi động scrcpy với --clipboard-only
2. Copy text trên Android (long press -> Copy)
3. Mở Notepad trên Windows
4. Paste (Ctrl+V)

Expected: Text từ Android xuất hiện trên Windows
```

### Test 4: Bidirectional Sync
```
1. Khởi động scrcpy với --clipboard-only
2. Copy "Hello from Windows" trên Windows
3. Paste trên Android -> should show "Hello from Windows"
4. Copy "Hello from Android" trên Android  
5. Paste trên Windows -> should show "Hello from Android"
6. Copy "Back to Windows" trên Windows
7. Paste trên Android -> should show "Back to Windows"

Expected: Clipboard sync hoạt động liên tục theo cả hai chiều
```

### Test 5: Performance và Stability
```
1. Khởi động scrcpy với --clipboard-only
2. Copy/paste nhiều lần liên tiếp (>20 lần)
3. Copy text dài (>1000 characters)
4. Copy special characters và Unicode
5. Để chạy trong thời gian dài (>30 phút)

Expected: 
- Không có memory leak
- Không có crash
- Sync vẫn hoạt động ổn định
- CPU usage thấp khi idle
```

### Test 6: Edge Cases
```
1. Copy empty string
2. Copy text với newlines và special characters
3. Copy very long text (>10KB)
4. Disconnect/reconnect device trong khi clipboard-only đang chạy
5. Copy binary data (images) - should be ignored gracefully

Expected: App không crash và xử lý gracefully
```

### Test 7: Exit Conditions
```
1. Khởi động scrcpy với --clipboard-only
2. Press Ctrl+C trong console
3. Close terminal window
4. Disconnect device

Expected: App thoát cleanly, không có zombie processes
```

## Debug Commands

### Kiểm tra ADB connection
```bash
adb devices
```

### Kiểm tra scrcpy server trên device
```bash
adb shell ps | grep scrcpy
```

### Kiểm tra logs
```bash
# Chạy với verbose logging
scrcpy --clipboard-only --verbosity=debug
```

## Troubleshooting

### Clipboard không sync
1. Kiểm tra ADB connection
2. Kiểm tra device authorization
3. Restart scrcpy
4. Kiểm tra Android version (cần >= 7.0)

### Performance issues
1. Kiểm tra CPU usage với Task Manager
2. Kiểm tra memory usage
3. Kiểm tra network latency nếu dùng WiFi

### Build issues
1. Đảm bảo có Visual Studio Build Tools
2. Đảm bảo có Meson và Ninja
3. Kiểm tra dependencies (SDL2, FFmpeg, etc.)

## Expected Log Output

### Successful startup:
```
INFO: Clipboard monitor initialized
INFO: Clipboard monitor started  
INFO: Running in clipboard-only mode - no video/audio
INFO: Clipboard-only mode: waiting for clipboard changes...
INFO: Press Ctrl+C to quit
```

### Clipboard sync:
```
DEBUG: Clipboard updated - sending to device
INFO: Clipboard sent to device
```

### Clean exit:
```
DEBUG: User requested to quit
INFO: Clipboard monitoring stopped
```
