# Automatic Bidirectional Clipboard Sync for Scrcpy

## Tổng quan

Tính năng này mở rộng scrcpy để hỗ trợ đồng bộ clipboard tự động hai chiều giữa Windows và Android mà không cần can thiệp thủ công.

### Tính năng chính:
- **Automatic Windows → Android sync**: Khi copy text trên Windows, tự động sync tới Android clipboard
- **Bidirectional sync**: Vẫn giữ nguyên tính năng sync từ Android → Windows
- **Clipboard-only mode**: Chạy scrcpy chỉ để đồng bộ clipboard, không cần video/audio
- **Background operation**: <PERSON><PERSON> thể chạy như service trong background

## Cách sử dụng

### 1. Chế độ thông thường với clipboard monitoring
```bash
# Chạy scrcpy bình thường + automatic clipboard sync
scrcpy --clipboard-monitor

# Hoặc kết hợp với các options khác
scrcpy --clipboard-monitor --max-size=1024 --bit-rate=2M
```

### 2. Chế độ clipboard-only (k<PERSON><PERSON><PERSON><PERSON> nghị)
```bash
# Chạy chỉ để đồng bộ clipboard, không có video/audio
scrcpy --clipboard-only
```

Trong chế độ này:
- Không có cửa sổ video hiển thị
- Không stream audio
- Tiêu tốn ít tài nguyên hệ thống
- Có thể chạy trong background như service
- Tự động bật clipboard monitoring

### 3. Tắt clipboard monitoring
```bash
# Chạy scrcpy bình thường mà không có automatic sync
scrcpy --no-clipboard-autosync
```

## Cách hoạt động

### Windows → Android Sync
1. Khi user copy text trên Windows (Ctrl+C, right-click copy, etc.)
2. Windows clipboard monitor phát hiện thay đổi
3. Tự động gửi text tới Android device
4. Text được set vào Android clipboard
5. User có thể paste trực tiếp trên Android (long press → Paste)

### Android → Windows Sync  
1. Khi user copy text trên Android
2. Scrcpy nhận clipboard data từ device
3. Tự động set vào Windows clipboard
4. User có thể paste trên Windows (Ctrl+V)

## Yêu cầu hệ thống

- **Windows**: Windows 7 trở lên (khuyến nghị Windows 10+)
- **Android**: Android 7.0 (API 24) trở lên
- **ADB**: Android Debug Bridge đã được cài đặt
- **USB Debugging**: Đã được bật trên Android device

## Build từ source

### Prerequisites
1. Visual Studio Build Tools hoặc Visual Studio Community
2. Meson build system
3. Ninja build tool
4. SDL2, FFmpeg libraries

### Build steps
```bash
# Clone repository
git clone https://github.com/Genymobile/scrcpy.git
cd scrcpy

# Run build script
build_windows.bat

# Hoặc build thủ công
meson setup build-win64 --cross-file=cross_win64.txt
meson compile -C build-win64
```

## Troubleshooting

### Clipboard không sync từ Windows → Android
1. Kiểm tra device đã được authorize với ADB
2. Đảm bảo scrcpy đang chạy với `--clipboard-monitor` hoặc `--clipboard-only`
3. Kiểm tra Android version >= 7.0
4. Restart scrcpy nếu cần

### Performance issues
1. Sử dụng `--clipboard-only` thay vì full scrcpy
2. Kiểm tra CPU usage - clipboard monitoring chỉ nên dùng <1% CPU khi idle
3. Nếu dùng WiFi, kiểm tra network latency

### Build errors
1. Đảm bảo chạy từ "Developer Command Prompt" của Visual Studio
2. Kiểm tra tất cả dependencies đã được cài đặt
3. Xem file `build_windows.bat` để biết chi tiết

## Ví dụ sử dụng

### Workflow 1: Development
```bash
# Chạy clipboard-only trong background
start /min scrcpy --clipboard-only

# Bây giờ có thể copy/paste tự do giữa Windows và Android
# Copy code từ IDE trên Windows → paste vào terminal app trên Android
# Copy error message từ Android → paste vào Google search trên Windows
```

### Workflow 2: Content sharing
```bash
# Chạy với video để thấy device screen
scrcpy --clipboard-monitor

# Copy URL từ browser trên Windows → paste vào browser trên Android
# Copy text từ Android app → paste vào document trên Windows
```

## Limitations

- Chỉ hỗ trợ text clipboard, không hỗ trợ images/files
- Cần kết nối ADB (USB hoặc WiFi)
- Windows-specific implementation (chưa hỗ trợ Linux/macOS)
- Cần Android 7.0+ cho clipboard API

## Security considerations

- Clipboard data được truyền qua ADB connection (encrypted nếu dùng ADB over WiFi với pairing)
- Không có logging clipboard content để bảo mật
- Chỉ sync khi scrcpy đang chạy và device được authorize

## Future improvements

- [ ] Hỗ trợ Linux và macOS
- [ ] Clipboard history
- [ ] Image clipboard sync
- [ ] Configurable sync filters
- [ ] System tray integration
- [ ] Auto-start with Windows
