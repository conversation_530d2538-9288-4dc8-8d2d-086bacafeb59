[{"name": "auto_features", "value": "auto", "section": "core", "machine": "any", "choices": ["enabled", "disabled", "auto"], "type": "combo", "description": "Override value of all 'auto' features"}, {"name": "backend", "value": "ninja", "section": "core", "machine": "any", "choices": ["ninja", "vs", "vs2010", "vs2012", "vs2013", "vs2015", "vs2017", "vs2019", "vs2022", "xcode", "none"], "type": "combo", "description": "Backend to use"}, {"name": "genvslite", "value": "vs2022", "section": "core", "machine": "any", "choices": ["vs2022"], "type": "combo", "description": "Setup multiple buildtype-suffixed ninja-backend build directories, and a [builddir]_vs containing a Visual Studio meta-backend with multiple configurations that calls into them"}, {"name": "buildtype", "value": "debug", "section": "core", "machine": "any", "choices": ["plain", "debug", "debugoptimized", "release", "minsize", "custom"], "type": "combo", "description": "Build type to use"}, {"name": "debug", "value": true, "section": "core", "machine": "any", "type": "boolean", "description": "Enable debug symbols and other information"}, {"name": "default_library", "value": "shared", "section": "core", "machine": "any", "choices": ["shared", "static", "both"], "type": "combo", "description": "Default library type"}, {"name": "default_both_libraries", "value": "shared", "section": "core", "machine": "any", "choices": ["shared", "static", "auto"], "type": "combo", "description": "Default library type for both_libraries"}, {"name": "install_umask", "value": 18, "section": "core", "machine": "any", "type": "integer", "description": "De<PERSON>ult umask to apply on permissions of installed files"}, {"name": "layout", "value": "mirror", "section": "core", "machine": "any", "choices": ["mirror", "flat"], "type": "combo", "description": "Build directory layout"}, {"name": "optimization", "value": "0", "section": "core", "machine": "any", "choices": ["plain", "0", "g", "1", "2", "3", "s"], "type": "combo", "description": "Optimization level"}, {"name": "prefer_static", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Whether to try static linking before shared linking"}, {"name": "strip", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Strip targets on install"}, {"name": "unity", "value": "off", "section": "core", "machine": "any", "choices": ["on", "off", "subprojects"], "type": "combo", "description": "Unity build"}, {"name": "unity_size", "value": 4, "section": "core", "machine": "any", "type": "integer", "description": "Unity block size"}, {"name": "warning_level", "value": "2", "section": "core", "machine": "any", "choices": ["0", "1", "2", "3", "everything"], "type": "combo", "description": "Compiler warning level to use"}, {"name": "werror", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Treat warnings as errors"}, {"name": "wrap_mode", "value": "default", "section": "core", "machine": "any", "choices": ["default", "nofallback", "nodownload", "forcefallback", "nopromote"], "type": "combo", "description": "Wrap mode"}, {"name": "force_fallback_for", "value": [], "section": "core", "machine": "any", "type": "array", "description": "Force fallback for those subprojects"}, {"name": "vsenv", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Activate Visual Studio environment"}, {"name": "pkgconfig.relocatable", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Generate pkgconfig files as relocatable"}, {"name": "python.bytecompile", "value": 0, "section": "core", "machine": "any", "type": "integer", "description": "Whether to compile bytecode"}, {"name": "python.install_env", "value": "prefix", "section": "core", "machine": "any", "choices": ["auto", "prefix", "system", "venv"], "type": "combo", "description": "Which python environment to install to"}, {"name": "python.platli<PERSON>", "value": "", "section": "core", "machine": "any", "type": "string", "description": "Directory for site-specific, platform-specific files."}, {"name": "python.pureli<PERSON>", "value": "", "section": "core", "machine": "any", "type": "string", "description": "Directory for site-specific, non-platform-specific files."}, {"name": "python.allow_limited_api", "value": true, "section": "core", "machine": "any", "type": "boolean", "description": "Whether to allow use of the Python Limited API"}, {"name": "pkg_config_path", "value": ["C:\\msys64\\mingw64\\lib\\pkgconfig", "C:\\msys64\\mingw64\\share\\pkgconfig"], "section": "core", "machine": "host", "type": "array", "description": "List of additional paths for pkg-config to search"}, {"name": "cmake_prefix_path", "value": [], "section": "core", "machine": "host", "type": "array", "description": "List of additional prefixes for cmake to search"}, {"name": "backend_max_links", "value": 0, "section": "backend", "machine": "any", "type": "integer", "description": "Maximum number of linker processes to run or 0 for no limit"}, {"name": "b_coverage", "value": false, "section": "base", "machine": "any", "type": "boolean", "description": "Enable coverage tracking."}, {"name": "b_pie", "value": false, "section": "base", "machine": "any", "type": "boolean", "description": "Build executables as position independent"}, {"name": "b_pch", "value": true, "section": "base", "machine": "any", "type": "boolean", "description": "Use precompiled headers"}, {"name": "b_sanitize", "value": [], "section": "base", "machine": "any", "type": "array", "description": "Code sanitizer to use"}, {"name": "b_ndebug", "value": "if-release", "section": "base", "machine": "any", "choices": ["true", "false", "if-release"], "type": "combo", "description": "Disable asserts"}, {"name": "b_colorout", "value": "always", "section": "base", "machine": "any", "choices": ["auto", "always", "never"], "type": "combo", "description": "Use colored output"}, {"name": "b_lto_threads", "value": 0, "section": "base", "machine": "any", "type": "integer", "description": "Use multiple threads for Link Time Optimization"}, {"name": "b_lto", "value": false, "section": "base", "machine": "any", "type": "boolean", "description": "Use link time optimization"}, {"name": "b_staticpic", "value": true, "section": "base", "machine": "any", "type": "boolean", "description": "Build static libraries as position independent"}, {"name": "b_pgo", "value": "off", "section": "base", "machine": "any", "choices": ["off", "generate", "use"], "type": "combo", "description": "Use profile guided optimization"}, {"name": "c_args", "value": [], "section": "compiler", "machine": "host", "type": "array", "description": "Extra arguments passed to the c compiler"}, {"name": "c_link_args", "value": [], "section": "compiler", "machine": "host", "type": "array", "description": "Extra arguments passed to the c linker"}, {"name": "c_std", "value": "c11", "section": "compiler", "machine": "host", "choices": ["none", "c89", "c99", "c11", "c17", "c18", "c2x", "c23", "c2y", "gnu89", "gnu99", "gnu11", "gnu17", "gnu18", "gnu2x", "gnu23", "gnu2y"], "type": "combo", "description": "c language standard to use"}, {"name": "c_winlibs", "value": ["-lkernel32", "-luser32", "-lgdi32", "-l<PERSON><PERSON><PERSON>", "-lshell32", "-lole32", "-loleaut32", "-lu<PERSON>", "-lcomdlg32", "-ladvapi32"], "section": "compiler", "machine": "host", "type": "array", "description": "Standard Windows libraries to link against"}, {"name": "prefix", "value": "C:/msys64/mingw64", "section": "directory", "machine": "any", "type": "string", "description": "Installation prefix"}, {"name": "bindir", "value": "bin", "section": "directory", "machine": "any", "type": "string", "description": "Executable directory"}, {"name": "datadir", "value": "share", "section": "directory", "machine": "any", "type": "string", "description": "Data file directory"}, {"name": "includedir", "value": "include", "section": "directory", "machine": "any", "type": "string", "description": "Header file directory"}, {"name": "infodir", "value": "share/info", "section": "directory", "machine": "any", "type": "string", "description": "Info page directory"}, {"name": "libdir", "value": "lib", "section": "directory", "machine": "any", "type": "string", "description": "Library directory"}, {"name": "licensedir", "value": "", "section": "directory", "machine": "any", "type": "string", "description": "Licenses directory"}, {"name": "libexecdir", "value": "libexec", "section": "directory", "machine": "any", "type": "string", "description": "Library executable directory"}, {"name": "localedir", "value": "share/locale", "section": "directory", "machine": "any", "type": "string", "description": "Locale data directory"}, {"name": "localstatedir", "value": "var", "section": "directory", "machine": "any", "type": "string", "description": "Localstate data directory"}, {"name": "mandir", "value": "share/man", "section": "directory", "machine": "any", "type": "string", "description": "Manual page directory"}, {"name": "sbindir", "value": "sbin", "section": "directory", "machine": "any", "type": "string", "description": "System executable directory"}, {"name": "sharedstatedir", "value": "com", "section": "directory", "machine": "any", "type": "string", "description": "Architecture-independent data directory"}, {"name": "sysconfdir", "value": "etc", "section": "directory", "machine": "any", "type": "string", "description": "Sysconf data directory"}, {"name": "compile_app", "value": true, "section": "user", "machine": "any", "type": "boolean", "description": "Build the client"}, {"name": "compile_server", "value": true, "section": "user", "machine": "any", "type": "boolean", "description": "Build the server"}, {"name": "prebuilt_server", "value": "", "section": "user", "machine": "any", "type": "string", "description": "Path of the prebuilt server"}, {"name": "portable", "value": false, "section": "user", "machine": "any", "type": "boolean", "description": "Use scrcpy-server from the same directory as the scrcpy executable"}, {"name": "static", "value": false, "section": "user", "machine": "any", "type": "boolean", "description": "Use static dependencies"}, {"name": "server_debugger", "value": false, "section": "user", "machine": "any", "type": "boolean", "description": "Run a server debugger and wait for a client to be attached"}, {"name": "v4l2", "value": true, "section": "user", "machine": "any", "type": "boolean", "description": "Enable V4L2 feature when supported"}, {"name": "usb", "value": true, "section": "user", "machine": "any", "type": "boolean", "description": "Enable HID/OTG features when supported"}, {"name": "errorlogs", "value": true, "section": "test", "machine": "any", "type": "boolean", "description": "Whether to print the logs from failing tests"}, {"name": "stdsplit", "value": true, "section": "test", "machine": "any", "type": "boolean", "description": "Split stdout and stderr in test logs"}]